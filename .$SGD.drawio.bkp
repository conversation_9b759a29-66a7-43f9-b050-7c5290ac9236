<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.6 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36" version="28.0.6">
  <diagram name="SGD Hardware Implementation" id="SGD_hw">
    <mxGraphModel dx="946" dy="1062" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="800" pageHeight="400" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="oVOXUxuvchOa5_stPHoB-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="init" target="wait_measure" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="260" y="20" />
              <mxPoint x="260" y="112" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="init" value="Initialize&lt;br&gt;Va₁,₀=Vc₁,₀=Va₂,n=Vc₂,n=0V&lt;br&gt;n=0" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="255" y="-40" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="wait_measure" target="va1_boundary" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="wait_measure" value="Wait &amp;amp; Measure&#xa;Iy,n" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="280" y="102" width="90" height="38" as="geometry" />
        </mxCell>
        <mxCell id="5RIp8YV-TdlPzCath6iw-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;" parent="1" source="va1_boundary" target="va1_reset" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5RIp8YV-TdlPzCath6iw-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;" parent="1" source="va1_boundary" target="va1_perturb" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="va1_boundary" value="Va₁,n at&#xa;boundary?" style="rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="290" y="36" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="va1_reset" value="Va₁,n+1=0V" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="401" y="25" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="va1_perturb" value="Va₁,n+1=Va₁,n+ΔV" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="401" y="69" width="90" height="25" as="geometry" />
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="va1_measure" target="va1_evaluate" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="va1_measure" value="Wait &amp;amp; Measure&#xa;Iy,n+1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="528" y="38" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="va1_evaluate" target="vc1_boundary" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="569" y="150" />
              <mxPoint x="325" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="va1_evaluate" target="va1_correct" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="va1_evaluate" value="Iy,n &amp;gt; Iy,n+1?" style="rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="529" y="102" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="va1_correct" target="vc1_boundary" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="451" y="150" />
              <mxPoint x="325" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="va1_correct" value="Va₁,n+1=Va₁,n-2ΔV" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="401" y="107" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vc1_boundary" value="Vc₁,n at&#xa;boundary?" style="rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="290" y="178" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="vc1_reset" value="Vc₁,n+1=0V" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="401" y="169" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="vc1_perturb" value="Vc₁,n+1=Vc₁,n+ΔV" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="401" y="209" width="90" height="25" as="geometry" />
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="vc1_measure" target="vc1_evaluate" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vc1_measure" value="Wait &amp;amp; Measure&#xa;Iy,n+1" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="529" y="183" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oVOXUxuvchOa5_stPHoB-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="vc1_evaluate" target="wait_measure" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vc1_evaluate" value="Iy,n &amp;gt; Iy,n+1?" style="rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="533" y="239" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="vc1_correct" value="Vc₁,n+1=Vc₁,n-2ΔV" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="401" y="244" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aux_continuous" value="Continuous Update&#xa;Va₂,n+1=f(Va₁,n+1)&#xa;Vc₂,n+1=f(Vc₁,n+1)&#xa;(Boundary Mapping)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="408" y="-40" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mapping_note" value="f(x): Max→4V,&amp;nbsp;&lt;div&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; Min→-4V,&amp;nbsp;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; else→0V&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#999999;fontStyle=2;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="518" y="-40" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="va1_reset" target="va1_measure" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="38" />
              <mxPoint x="500" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="va1_perturb" target="va1_measure" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="82" />
              <mxPoint x="500" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="vc1_boundary" target="vc1_reset" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="vc1_boundary" target="vc1_perturb" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="vc1_reset" target="vc1_measure" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="182" />
              <mxPoint x="500" y="203" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="vc1_perturb" target="vc1_measure" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="222" />
              <mxPoint x="500" y="203" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;" parent="1" source="vc1_evaluate" target="vc1_correct" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label1" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="358" y="35" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="label2" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="358" y="75" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="label3" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="509" y="106" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="label4" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="358" y="177" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="label5" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="358" y="219" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="label6" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="508" y="244" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="5RIp8YV-TdlPzCath6iw-3" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="574" y="281" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="5RIp8YV-TdlPzCath6iw-4" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="570" y="142" width="20" height="10" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
