<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.0//EN'
          'http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd'>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" style="fill-opacity:1; color-rendering:auto; color-interpolation:auto; text-rendering:auto; stroke:black; stroke-linecap:square; stroke-miterlimit:10; shape-rendering:auto; stroke-opacity:1; fill:black; stroke-dasharray:none; font-weight:normal; stroke-width:1; font-family:'Dialog'; font-style:normal; stroke-linejoin:miter; font-size:12px; stroke-dashoffset:0; image-rendering:auto;" width="1600" height="1200" xmlns="http://www.w3.org/2000/svg"
><!--Generated by the Batik Graphics2D SVG Generator--><defs id="genericDefs"
  /><g
  ><defs id="defs1"
    ><clipPath clipPathUnits="userSpaceOnUse" id="clipPath1"
      ><path d="M0 0 L1600 0 L1600 1200 L0 1200 L0 0 Z"
      /></clipPath
      ><font horiz-adv-x="75.0" id="font1"
      ><font-face ascent="100.53711" descent="21.972656" units-per-em="100" style="font-style:normal; font-family:Dialog; font-weight:normal;"
        /><missing-glyph horiz-adv-x="75.0" d="M12.5 0 L12.5 62.5 L62.5 62.5 L62.5 0 L12.5 0 ZM14.0625 1.5625 L60.9375 1.5625 L60.9375 60.9375 L14.0625 60.9375 L14.0625 1.5625 Z"
        /><glyph unicode="1" horiz-adv-x="55.615234" d="M37.25 0 L28.4688 0 L28.4688 56 Q25.2969 52.9844 20.1406 49.9531 Q14.9844 46.9219 10.8906 45.4062 L10.8906 53.9062 Q18.2656 57.375 23.7812 62.3047 Q29.2969 67.2344 31.5938 71.875 L37.25 71.875 L37.25 0 Z"
        /><glyph unicode="2" horiz-adv-x="55.615234" d="M50.3438 8.4531 L50.3438 0 L3.0312 0 Q2.9375 3.1719 4.0469 6.1094 Q5.8594 10.9375 9.8359 15.625 Q13.8125 20.3125 21.3438 26.4688 Q33.0156 36.0312 37.1172 41.625 Q41.2188 47.2188 41.2188 52.2031 Q41.2188 57.4219 37.4766 61.0078 Q33.7344 64.5938 27.7344 64.5938 Q21.3906 64.5938 17.5781 60.7891 Q13.7656 56.9844 13.7188 50.25 L4.6875 51.1719 Q5.6094 61.2812 11.6641 66.5781 Q17.7188 71.875 27.9375 71.875 Q38.2344 71.875 44.2422 66.1641 Q50.25 60.4531 50.25 52 Q50.25 47.7031 48.4922 43.5547 Q46.7344 39.4062 42.6562 34.8125 Q38.5781 30.2188 29.1094 22.2188 Q21.1875 15.5781 18.9453 13.2109 Q16.7031 10.8438 15.2344 8.4531 L50.3438 8.4531 Z"
        /><glyph unicode="3" horiz-adv-x="55.615234" d="M4.2031 18.8906 L12.9844 20.0625 Q14.5 12.5938 18.1406 9.2969 Q21.7812 6 27 6 Q33.2031 6 37.4766 10.2969 Q41.75 14.5938 41.75 20.9531 Q41.75 27 37.7969 30.9297 Q33.8438 34.8594 27.7344 34.8594 Q25.25 34.8594 21.5312 33.8906 L22.5156 41.6094 Q23.3906 41.5 23.9219 41.5 Q29.5469 41.5 34.0391 44.4297 Q38.5312 47.3594 38.5312 53.4688 Q38.5312 58.2969 35.2578 61.4766 Q31.9844 64.6562 26.8125 64.6562 Q21.6875 64.6562 18.2656 61.4297 Q14.8438 58.2031 13.875 51.7656 L5.0781 53.3281 Q6.6875 62.1562 12.3984 67.0156 Q18.1094 71.875 26.6094 71.875 Q32.4688 71.875 37.3984 69.3594 Q42.3281 66.8438 44.9453 62.5 Q47.5625 58.1562 47.5625 53.2656 Q47.5625 48.6406 45.0703 44.8281 Q42.5781 41.0156 37.7031 38.7656 Q44.0469 37.3125 47.5625 32.6953 Q51.0781 28.0781 51.0781 21.1406 Q51.0781 11.7656 44.2422 5.25 Q37.4062 -1.2656 26.9531 -1.2656 Q17.5312 -1.2656 11.3047 4.3516 Q5.0781 9.9688 4.2031 18.8906 Z"
        /><glyph unicode="4" horiz-adv-x="55.615234" d="M32.3281 0 L32.3281 17.1406 L1.2656 17.1406 L1.2656 25.2031 L33.9375 71.5781 L41.1094 71.5781 L41.1094 25.2031 L50.7812 25.2031 L50.7812 17.1406 L41.1094 17.1406 L41.1094 0 L32.3281 0 ZM32.3281 25.2031 L32.3281 57.4688 L9.9062 25.2031 L32.3281 25.2031 Z"
        /><glyph unicode="5" horiz-adv-x="55.615234" d="M4.1562 18.75 L13.375 19.5312 Q14.4062 12.7969 18.1406 9.3984 Q21.875 6 27.1562 6 Q33.5 6 37.8906 10.7891 Q42.2812 15.5781 42.2812 23.4844 Q42.2812 31 38.0625 35.3516 Q33.8438 39.7031 27 39.7031 Q22.75 39.7031 19.3359 37.7734 Q15.9219 35.8438 13.9688 32.7656 L5.7188 33.8438 L12.6406 70.6094 L48.25 70.6094 L48.25 62.2031 L19.6719 62.2031 L15.8281 42.9688 Q22.2656 47.4688 29.3438 47.4688 Q38.7188 47.4688 45.1641 40.9688 Q51.6094 34.4688 51.6094 24.2656 Q51.6094 14.5469 45.9531 7.4688 Q39.0625 -1.2188 27.1562 -1.2188 Q17.3906 -1.2188 11.2109 4.25 Q5.0312 9.7188 4.1562 18.75 Z"
        /><glyph unicode="6" horiz-adv-x="55.615234" d="M49.75 54.0469 L41.0156 53.375 Q39.8438 58.5469 37.7031 60.8906 Q34.125 64.6562 28.9062 64.6562 Q24.7031 64.6562 21.5312 62.3125 Q17.3906 59.2812 14.9922 53.4688 Q12.5938 47.6562 12.5 36.9219 Q15.6719 41.75 20.2656 44.0938 Q24.8594 46.4375 29.8906 46.4375 Q38.6719 46.4375 44.8516 39.9688 Q51.0312 33.5 51.0312 23.25 Q51.0312 16.5 48.125 10.7188 Q45.2188 4.9375 40.1406 1.8594 Q35.0625 -1.2188 28.6094 -1.2188 Q17.625 -1.2188 10.6953 6.8594 Q3.7656 14.9375 3.7656 33.5 Q3.7656 54.25 11.4219 63.6719 Q18.1094 71.875 29.4375 71.875 Q37.8906 71.875 43.2891 67.1406 Q48.6875 62.4062 49.75 54.0469 ZM13.875 23.1875 Q13.875 18.6562 15.7969 14.5078 Q17.7188 10.3594 21.1875 8.1797 Q24.6562 6 28.4688 6 Q34.0312 6 38.0391 10.4922 Q42.0469 14.9844 42.0469 22.7031 Q42.0469 30.125 38.0859 34.3984 Q34.125 38.6719 28.125 38.6719 Q22.1719 38.6719 18.0234 34.3984 Q13.875 30.125 13.875 23.1875 Z"
        /><glyph unicode="7" horiz-adv-x="55.615234" d="M4.7344 62.2031 L4.7344 70.6562 L51.0781 70.6562 L51.0781 63.8125 Q44.2344 56.5469 37.5234 44.4844 Q30.8125 32.4219 27.1562 19.6719 Q24.5156 10.6875 23.7812 0 L14.75 0 Q14.8906 8.4531 18.0625 20.4141 Q21.2344 32.375 27.1719 43.4844 Q33.1094 54.5938 39.7969 62.2031 L4.7344 62.2031 Z"
        /><glyph unicode="." horiz-adv-x="27.783203" d="M9.0781 0 L9.0781 10.0156 L19.0938 10.0156 L19.0938 0 L9.0781 0 Z"
        /><glyph unicode="8" horiz-adv-x="55.615234" d="M17.6719 38.8125 Q12.2031 40.8281 9.5703 44.5391 Q6.9375 48.25 6.9375 53.4219 Q6.9375 61.2344 12.5547 66.5547 Q18.1719 71.875 27.4844 71.875 Q36.8594 71.875 42.5781 66.4297 Q48.2969 60.9844 48.2969 53.1719 Q48.2969 48.1875 45.6797 44.5078 Q43.0625 40.8281 37.75 38.8125 Q44.3438 36.6719 47.7812 31.8828 Q51.2188 27.0938 51.2188 20.4531 Q51.2188 11.2812 44.7266 5.0312 Q38.2344 -1.2188 27.6406 -1.2188 Q17.0469 -1.2188 10.5469 5.0547 Q4.0469 11.3281 4.0469 20.7031 Q4.0469 27.6875 7.5938 32.3984 Q11.1406 37.1094 17.6719 38.8125 ZM15.9219 53.7188 Q15.9219 48.6406 19.1953 45.4141 Q22.4688 42.1875 27.6875 42.1875 Q32.7656 42.1875 36.0156 45.3828 Q39.2656 48.5781 39.2656 53.2188 Q39.2656 58.0625 35.9141 61.3594 Q32.5625 64.6562 27.5938 64.6562 Q22.5625 64.6562 19.2422 61.4297 Q15.9219 58.2031 15.9219 53.7188 ZM13.0938 20.6562 Q13.0938 16.8906 14.875 13.375 Q16.6562 9.8594 20.1719 7.9297 Q23.6875 6 27.7344 6 Q34.0312 6 38.1328 10.0547 Q42.2344 14.1094 42.2344 20.3594 Q42.2344 26.7031 38.0156 30.8594 Q33.7969 35.0156 27.4375 35.0156 Q21.2344 35.0156 17.1641 30.9141 Q13.0938 26.8125 13.0938 20.6562 Z"
      /></font
    ></defs
    ><g style="fill:white; stroke:white;"
    ><rect x="0" y="0" width="1600" style="clip-path:url(#clipPath1); stroke:none;" height="1200"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><rect x="0" width="1600" height="1200" y="0" style="stroke:none;"
      /><path style="stroke:none;" d="M208 1068 L1448 1068 L1448 90 L208 90 Z"
    /></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><line y2="1068.0009" style="fill:none;" x1="208" x2="1448" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="208" x2="1448" y1="89.9992"
      /><line y2="1055.6022" style="fill:none;" x1="208" x2="208" y1="1068.0009"
      /><line y2="1055.6022" style="fill:none;" x1="414.6667" x2="414.6667" y1="1068.0009"
      /><line y2="1055.6022" style="fill:none;" x1="621.3333" x2="621.3333" y1="1068.0009"
      /><line y2="1055.6022" style="fill:none;" x1="828" x2="828" y1="1068.0009"
      /><line y2="1055.6022" style="fill:none;" x1="1034.6666" x2="1034.6666" y1="1068.0009"
      /><line y2="1055.6022" style="fill:none;" x1="1241.3334" x2="1241.3334" y1="1068.0009"
      /><line y2="1055.6022" style="fill:none;" x1="1448" x2="1448" y1="1068.0009"
      /><line y2="102.3978" style="fill:none;" x1="208" x2="208" y1="89.9992"
      /><line y2="102.3978" style="fill:none;" x1="414.6667" x2="414.6667" y1="89.9992"
      /><line y2="102.3978" style="fill:none;" x1="621.3333" x2="621.3333" y1="89.9992"
      /><line y2="102.3978" style="fill:none;" x1="828" x2="828" y1="89.9992"
      /><line y2="102.3978" style="fill:none;" x1="1034.6666" x2="1034.6666" y1="89.9992"
      /><line y2="102.3978" style="fill:none;" x1="1241.3334" x2="1241.3334" y1="89.9992"
      /><line y2="102.3978" style="fill:none;" x1="1448" x2="1448" y1="89.9992"
    /></g
    ><g transform="translate(208,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >1</text
    ></g
    ><g transform="translate(414.6667,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >2</text
    ></g
    ><g transform="translate(621.3333,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >3</text
    ></g
    ><g transform="translate(828,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >4</text
    ></g
    ><g transform="translate(1034.6666,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >5</text
    ></g
    ><g transform="translate(1241.3334,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >6</text
    ></g
    ><g transform="translate(1448,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >7</text
    ></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><line y2="89.9992" style="fill:none;" x1="208" x2="208" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1448" x2="1448" y1="1068.0009"
      /><line y2="1068.0009" style="fill:none;" x1="208" x2="220.4" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="208" x2="220.4" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="208" x2="220.4" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="208" x2="220.4" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="208" x2="220.4" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="208" x2="220.4" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="208" x2="220.4" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="208" x2="220.4" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="208" x2="220.4" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="208" x2="220.4" y1="89.9992"
      /><line y2="1068.0009" style="fill:none;" x1="1448" x2="1435.6" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="1448" x2="1435.6" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="1448" x2="1435.6" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="1448" x2="1435.6" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="1448" x2="1435.6" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="1448" x2="1435.6" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="1448" x2="1435.6" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="1448" x2="1435.6" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="1448" x2="1435.6" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="1448" x2="1435.6" y1="89.9992"
    /></g
    ><g transform="translate(197.3333,1068.0009)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.4</text
    ></g
    ><g transform="translate(197.3333,959.3317)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.45</text
    ></g
    ><g transform="translate(197.3333,850.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.5</text
    ></g
    ><g transform="translate(197.3333,742.0016)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.55</text
    ></g
    ><g transform="translate(197.3333,633.3325)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.6</text
    ></g
    ><g transform="translate(197.3333,524.6675)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.65</text
    ></g
    ><g transform="translate(197.3333,415.9984)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.7</text
    ></g
    ><g transform="translate(197.3333,307.3333)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.75</text
    ></g
    ><g transform="translate(197.3333,198.6683)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.8</text
    ></g
    ><g transform="translate(197.3333,89.9992)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.85</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><path d="M208 183.6706 L414.6667 161.7212 L621.3333 179.1066 L828 257.3451 L1034.6666 236.7015 L1241.3334 263.8657 L1448 268.2141" style="fill:none; fill-rule:evenodd;"
      /><circle transform="translate(208,183.6706)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(414.6667,161.7212)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(621.3333,179.1066)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(828,257.3451)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1034.6666,236.7015)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1241.3334,263.8657)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1448,268.2141)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><path d="M208 350.801 L414.6667 320.3745 L621.3333 448.6012 L828 494.241 L1034.6666 392.0924 L1241.3334 422.5189 L1448 370.3586" style="fill:none; fill-rule:evenodd; stroke:rgb(217,83,25);"
      /><circle transform="translate(208,350.801)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(414.6667,320.3745)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(621.3333,448.6012)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(828,494.241)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1034.6666,392.0924)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1241.3334,422.5189)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1448,370.3586)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><path d="M208 511.6264 L414.6667 529.0118 L621.3333 546.4013 L828 692.0134 L1034.6666 539.8807 L1241.3334 518.1469 L1448 548.5735" style="fill:none; fill-rule:evenodd; stroke:rgb(237,177,32);"
      /><circle transform="translate(208,511.6264)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(414.6667,529.0118)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(621.3333,546.4013)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(828,692.0134)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1034.6666,539.8807)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1241.3334,518.1469)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1448,548.5735)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><path d="M208 781.1209 L414.6667 715.9194 L621.3333 1018.0126 L828 881.0932 L1034.6666 692.0134 L1241.3334 628.9882 L1448 722.4399" style="fill:none; fill-rule:evenodd; stroke:rgb(126,47,142);"
      /><circle transform="translate(208,781.1209)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(414.6667,715.9194)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(621.3333,1018.0126)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(828,881.0932)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1034.6666,692.0134)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1241.3334,628.9882)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1448,722.4399)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
    /></g
  ></g
></svg
>
