<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.0//EN'
          'http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd'>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" style="fill-opacity:1; color-rendering:auto; color-interpolation:auto; text-rendering:auto; stroke:black; stroke-linecap:square; stroke-miterlimit:10; shape-rendering:auto; stroke-opacity:1; fill:black; stroke-dasharray:none; font-weight:normal; stroke-width:1; font-family:'Dialog'; font-style:normal; stroke-linejoin:miter; font-size:12px; stroke-dashoffset:0; image-rendering:auto;" width="1800" height="1200" xmlns="http://www.w3.org/2000/svg"
><!--Generated by the Batik Graphics2D SVG Generator--><defs id="genericDefs"
  /><g
  ><defs id="defs1"
    ><clipPath clipPathUnits="userSpaceOnUse" id="clipPath1"
      ><path d="M0 0 L1800 0 L1800 1200 L0 1200 L0 0 Z"
      /></clipPath
      ><font horiz-adv-x="75.0" id="font1"
      ><font-face ascent="100.53711" descent="21.972656" units-per-em="100" style="font-style:normal; font-family:Dialog; font-weight:normal;"
        /><missing-glyph horiz-adv-x="75.0" d="M12.5 0 L12.5 62.5 L62.5 62.5 L62.5 0 L12.5 0 ZM14.0625 1.5625 L60.9375 1.5625 L60.9375 60.9375 L14.0625 60.9375 L14.0625 1.5625 Z"
        /><glyph unicode="1" horiz-adv-x="55.615234" d="M37.25 0 L28.4688 0 L28.4688 56 Q25.2969 52.9844 20.1406 49.9531 Q14.9844 46.9219 10.8906 45.4062 L10.8906 53.9062 Q18.2656 57.375 23.7812 62.3047 Q29.2969 67.2344 31.5938 71.875 L37.25 71.875 L37.25 0 Z"
        /><glyph unicode="2" horiz-adv-x="55.615234" d="M50.3438 8.4531 L50.3438 0 L3.0312 0 Q2.9375 3.1719 4.0469 6.1094 Q5.8594 10.9375 9.8359 15.625 Q13.8125 20.3125 21.3438 26.4688 Q33.0156 36.0312 37.1172 41.625 Q41.2188 47.2188 41.2188 52.2031 Q41.2188 57.4219 37.4766 61.0078 Q33.7344 64.5938 27.7344 64.5938 Q21.3906 64.5938 17.5781 60.7891 Q13.7656 56.9844 13.7188 50.25 L4.6875 51.1719 Q5.6094 61.2812 11.6641 66.5781 Q17.7188 71.875 27.9375 71.875 Q38.2344 71.875 44.2422 66.1641 Q50.25 60.4531 50.25 52 Q50.25 47.7031 48.4922 43.5547 Q46.7344 39.4062 42.6562 34.8125 Q38.5781 30.2188 29.1094 22.2188 Q21.1875 15.5781 18.9453 13.2109 Q16.7031 10.8438 15.2344 8.4531 L50.3438 8.4531 Z"
        /><glyph unicode="3" horiz-adv-x="55.615234" d="M4.2031 18.8906 L12.9844 20.0625 Q14.5 12.5938 18.1406 9.2969 Q21.7812 6 27 6 Q33.2031 6 37.4766 10.2969 Q41.75 14.5938 41.75 20.9531 Q41.75 27 37.7969 30.9297 Q33.8438 34.8594 27.7344 34.8594 Q25.25 34.8594 21.5312 33.8906 L22.5156 41.6094 Q23.3906 41.5 23.9219 41.5 Q29.5469 41.5 34.0391 44.4297 Q38.5312 47.3594 38.5312 53.4688 Q38.5312 58.2969 35.2578 61.4766 Q31.9844 64.6562 26.8125 64.6562 Q21.6875 64.6562 18.2656 61.4297 Q14.8438 58.2031 13.875 51.7656 L5.0781 53.3281 Q6.6875 62.1562 12.3984 67.0156 Q18.1094 71.875 26.6094 71.875 Q32.4688 71.875 37.3984 69.3594 Q42.3281 66.8438 44.9453 62.5 Q47.5625 58.1562 47.5625 53.2656 Q47.5625 48.6406 45.0703 44.8281 Q42.5781 41.0156 37.7031 38.7656 Q44.0469 37.3125 47.5625 32.6953 Q51.0781 28.0781 51.0781 21.1406 Q51.0781 11.7656 44.2422 5.25 Q37.4062 -1.2656 26.9531 -1.2656 Q17.5312 -1.2656 11.3047 4.3516 Q5.0781 9.9688 4.2031 18.8906 Z"
        /><glyph unicode="4" horiz-adv-x="55.615234" d="M32.3281 0 L32.3281 17.1406 L1.2656 17.1406 L1.2656 25.2031 L33.9375 71.5781 L41.1094 71.5781 L41.1094 25.2031 L50.7812 25.2031 L50.7812 17.1406 L41.1094 17.1406 L41.1094 0 L32.3281 0 ZM32.3281 25.2031 L32.3281 57.4688 L9.9062 25.2031 L32.3281 25.2031 Z"
        /><glyph unicode="5" horiz-adv-x="55.615234" d="M4.1562 18.75 L13.375 19.5312 Q14.4062 12.7969 18.1406 9.3984 Q21.875 6 27.1562 6 Q33.5 6 37.8906 10.7891 Q42.2812 15.5781 42.2812 23.4844 Q42.2812 31 38.0625 35.3516 Q33.8438 39.7031 27 39.7031 Q22.75 39.7031 19.3359 37.7734 Q15.9219 35.8438 13.9688 32.7656 L5.7188 33.8438 L12.6406 70.6094 L48.25 70.6094 L48.25 62.2031 L19.6719 62.2031 L15.8281 42.9688 Q22.2656 47.4688 29.3438 47.4688 Q38.7188 47.4688 45.1641 40.9688 Q51.6094 34.4688 51.6094 24.2656 Q51.6094 14.5469 45.9531 7.4688 Q39.0625 -1.2188 27.1562 -1.2188 Q17.3906 -1.2188 11.2109 4.25 Q5.0312 9.7188 4.1562 18.75 Z"
        /><glyph unicode="6" horiz-adv-x="55.615234" d="M49.75 54.0469 L41.0156 53.375 Q39.8438 58.5469 37.7031 60.8906 Q34.125 64.6562 28.9062 64.6562 Q24.7031 64.6562 21.5312 62.3125 Q17.3906 59.2812 14.9922 53.4688 Q12.5938 47.6562 12.5 36.9219 Q15.6719 41.75 20.2656 44.0938 Q24.8594 46.4375 29.8906 46.4375 Q38.6719 46.4375 44.8516 39.9688 Q51.0312 33.5 51.0312 23.25 Q51.0312 16.5 48.125 10.7188 Q45.2188 4.9375 40.1406 1.8594 Q35.0625 -1.2188 28.6094 -1.2188 Q17.625 -1.2188 10.6953 6.8594 Q3.7656 14.9375 3.7656 33.5 Q3.7656 54.25 11.4219 63.6719 Q18.1094 71.875 29.4375 71.875 Q37.8906 71.875 43.2891 67.1406 Q48.6875 62.4062 49.75 54.0469 ZM13.875 23.1875 Q13.875 18.6562 15.7969 14.5078 Q17.7188 10.3594 21.1875 8.1797 Q24.6562 6 28.4688 6 Q34.0312 6 38.0391 10.4922 Q42.0469 14.9844 42.0469 22.7031 Q42.0469 30.125 38.0859 34.3984 Q34.125 38.6719 28.125 38.6719 Q22.1719 38.6719 18.0234 34.3984 Q13.875 30.125 13.875 23.1875 Z"
        /><glyph unicode="7" horiz-adv-x="55.615234" d="M4.7344 62.2031 L4.7344 70.6562 L51.0781 70.6562 L51.0781 63.8125 Q44.2344 56.5469 37.5234 44.4844 Q30.8125 32.4219 27.1562 19.6719 Q24.5156 10.6875 23.7812 0 L14.75 0 Q14.8906 8.4531 18.0625 20.4141 Q21.2344 32.375 27.1719 43.4844 Q33.1094 54.5938 39.7969 62.2031 L4.7344 62.2031 Z"
        /><glyph unicode="." horiz-adv-x="27.783203" d="M9.0781 0 L9.0781 10.0156 L19.0938 10.0156 L19.0938 0 L9.0781 0 Z"
        /><glyph unicode="8" horiz-adv-x="55.615234" d="M17.6719 38.8125 Q12.2031 40.8281 9.5703 44.5391 Q6.9375 48.25 6.9375 53.4219 Q6.9375 61.2344 12.5547 66.5547 Q18.1719 71.875 27.4844 71.875 Q36.8594 71.875 42.5781 66.4297 Q48.2969 60.9844 48.2969 53.1719 Q48.2969 48.1875 45.6797 44.5078 Q43.0625 40.8281 37.75 38.8125 Q44.3438 36.6719 47.7812 31.8828 Q51.2188 27.0938 51.2188 20.4531 Q51.2188 11.2812 44.7266 5.0312 Q38.2344 -1.2188 27.6406 -1.2188 Q17.0469 -1.2188 10.5469 5.0547 Q4.0469 11.3281 4.0469 20.7031 Q4.0469 27.6875 7.5938 32.3984 Q11.1406 37.1094 17.6719 38.8125 ZM15.9219 53.7188 Q15.9219 48.6406 19.1953 45.4141 Q22.4688 42.1875 27.6875 42.1875 Q32.7656 42.1875 36.0156 45.3828 Q39.2656 48.5781 39.2656 53.2188 Q39.2656 58.0625 35.9141 61.3594 Q32.5625 64.6562 27.5938 64.6562 Q22.5625 64.6562 19.2422 61.4297 Q15.9219 58.2031 15.9219 53.7188 ZM13.0938 20.6562 Q13.0938 16.8906 14.875 13.375 Q16.6562 9.8594 20.1719 7.9297 Q23.6875 6 27.7344 6 Q34.0312 6 38.1328 10.0547 Q42.2344 14.1094 42.2344 20.3594 Q42.2344 26.7031 38.0156 30.8594 Q33.7969 35.0156 27.4375 35.0156 Q21.2344 35.0156 17.1641 30.9141 Q13.0938 26.8125 13.0938 20.6562 Z"
        /><glyph unicode="z" horiz-adv-x="50.0" d="M1.9531 0 L1.9531 7.125 L34.9688 45.0156 Q29.3438 44.7344 25.0469 44.7344 L3.9062 44.7344 L3.9062 51.8594 L46.2969 51.8594 L46.2969 46.0469 L18.2188 13.1406 L12.7969 7.125 Q18.7031 7.5625 23.875 7.5625 L47.8594 7.5625 L47.8594 0 L1.9531 0 Z"
        /><glyph unicode="H" horiz-adv-x="72.2168" d="M8.0156 0 L8.0156 71.5781 L17.4844 71.5781 L17.4844 42.1875 L54.6875 42.1875 L54.6875 71.5781 L64.1562 71.5781 L64.1562 0 L54.6875 0 L54.6875 33.7344 L17.4844 33.7344 L17.4844 0 L8.0156 0 Z"
        /><glyph unicode="k" horiz-adv-x="50.0" d="M6.6406 0 L6.6406 71.5781 L15.4375 71.5781 L15.4375 30.7656 L36.2344 51.8594 L47.6094 51.8594 L27.7812 32.625 L49.6094 0 L38.7656 0 L21.625 26.5156 L15.4375 20.5625 L15.4375 0 L6.6406 0 Z"
        /><glyph unicode=" " horiz-adv-x="27.783203" d=""
        /><glyph unicode="0" horiz-adv-x="55.615234" d="M4.1562 35.2969 Q4.1562 48 6.7656 55.7422 Q9.375 63.4844 14.5234 67.6797 Q19.6719 71.875 27.4844 71.875 Q33.25 71.875 37.5938 69.5547 Q41.9375 67.2344 44.7734 62.8672 Q47.6094 58.5 49.2188 52.2266 Q50.8281 45.9531 50.8281 35.2969 Q50.8281 22.7031 48.2422 14.9688 Q45.6562 7.2344 40.5078 3.0078 Q35.3594 -1.2188 27.4844 -1.2188 Q17.1406 -1.2188 11.2344 6.2031 Q4.1562 15.1406 4.1562 35.2969 ZM13.1875 35.2969 Q13.1875 17.6719 17.3125 11.8359 Q21.4375 6 27.4844 6 Q33.5469 6 37.6719 11.8594 Q41.7969 17.7188 41.7969 35.2969 Q41.7969 52.9844 37.6719 58.7891 Q33.5469 64.5938 27.3906 64.5938 Q21.3438 64.5938 17.7188 59.4688 Q13.1875 52.9375 13.1875 35.2969 Z"
      /></font
      ><font horiz-adv-x="77.7832" id="font2"
      ><font-face ascent="91.23535" descent="19.506836" units-per-em="100" style="font-style:normal; font-family:Times New Roman; font-weight:normal;"
        /><missing-glyph horiz-adv-x="77.7832" d="M13.875 0 L13.875 62.5 L63.875 62.5 L63.875 0 L13.875 0 ZM15.4375 1.5625 L62.3125 1.5625 L62.3125 60.9375 L15.4375 60.9375 L15.4375 1.5625 Z"
        /><glyph unicode=")" horiz-adv-x="33.30078" d="M2.25 67.3906 L2.25 69.4375 Q9.6719 65.7656 14.5938 60.7969 Q21.5781 53.6562 25.3906 44.0625 Q29.2031 34.4688 29.2031 24.0781 Q29.2031 8.9375 21.7578 -3.5391 Q14.3125 -16.0156 2.25 -21.3906 L2.25 -19.5781 Q8.25 -16.2188 12.1328 -10.4766 Q16.0156 -4.7344 17.8984 4.125 Q19.7812 12.9844 19.7812 22.6094 Q19.7812 33.0156 18.1719 41.6094 Q16.9375 48.3438 15.1094 52.3984 Q13.2812 56.4531 10.2578 60.2109 Q7.2344 63.9688 2.25 67.3906 Z"
        /><glyph unicode="n" horiz-adv-x="50.0" d="M16.1562 36.5781 Q24.0312 46.0469 31.1562 46.0469 Q34.8125 46.0469 37.4531 44.2188 Q40.0938 42.3906 41.6562 38.1875 Q42.7188 35.25 42.7188 29.2031 L42.7188 10.1094 Q42.7188 5.8594 43.4062 4.3438 Q43.9531 3.125 45.1484 2.4453 Q46.3438 1.7656 49.5625 1.7656 L49.5625 0 L27.4375 0 L27.4375 1.7656 L28.375 1.7656 Q31.5 1.7656 32.7422 2.7109 Q33.9844 3.6562 34.4688 5.5156 Q34.6719 6.25 34.6719 10.1094 L34.6719 28.4219 Q34.6719 34.5156 33.0859 37.2812 Q31.5 40.0469 27.7344 40.0469 Q21.9219 40.0469 16.1562 33.6875 L16.1562 10.1094 Q16.1562 5.5625 16.7031 4.5 Q17.3906 3.0781 18.5859 2.4219 Q19.7812 1.7656 23.4375 1.7656 L23.4375 0 L1.3125 0 L1.3125 1.7656 L2.2969 1.7656 Q5.7188 1.7656 6.9141 3.4922 Q8.1094 5.2188 8.1094 10.1094 L8.1094 26.7031 Q8.1094 34.7656 7.7422 36.5234 Q7.375 38.2812 6.6172 38.9141 Q5.8594 39.5469 4.5938 39.5469 Q3.2188 39.5469 1.3125 38.8125 L0.5938 40.5781 L14.0625 46.0469 L16.1562 46.0469 L16.1562 36.5781 Z"
        /><glyph unicode="(" horiz-adv-x="33.30078" d="M31.0625 -19.5781 L31.0625 -21.3906 Q23.6875 -17.6719 18.75 -12.7031 Q11.7188 -5.6094 7.9141 4.0078 Q4.1094 13.625 4.1094 23.9688 Q4.1094 39.1094 11.5781 51.5859 Q19.0469 64.0625 31.0625 69.4375 L31.0625 67.3906 Q25.0469 64.0625 21.1875 58.3047 Q17.3281 52.5469 15.4297 43.7031 Q13.5312 34.8594 13.5312 25.25 Q13.5312 14.7969 15.1406 6.25 Q16.4062 -0.4844 18.2109 -4.5625 Q20.0156 -8.6406 23.0703 -12.3984 Q26.125 -16.1562 31.0625 -19.5781 Z"
        /><glyph unicode=" " horiz-adv-x="25.0" d=""
        /><glyph unicode="e" horiz-adv-x="44.384766" d="M10.6406 27.875 Q10.5938 17.9219 15.4844 12.25 Q20.3594 6.5938 26.9531 6.5938 Q31.3438 6.5938 34.5938 9.0078 Q37.8438 11.4219 40.0469 17.2812 L41.5469 16.3125 Q40.5312 9.625 35.6016 4.125 Q30.6719 -1.375 23.25 -1.375 Q15.1875 -1.375 9.4531 4.9062 Q3.7188 11.1875 3.7188 21.7812 Q3.7188 33.25 9.6016 39.6719 Q15.4844 46.0938 24.3594 46.0938 Q31.8906 46.0938 36.7188 41.1406 Q41.5469 36.1875 41.5469 27.875 L10.6406 27.875 ZM10.6406 30.7188 L31.3438 30.7188 Q31.1094 35.0156 30.3281 36.7656 Q29.1094 39.5 26.6875 41.0625 Q24.2656 42.625 21.625 42.625 Q17.5781 42.625 14.3828 39.4766 Q11.1875 36.3281 10.6406 30.7188 Z"
        /><glyph unicode="m" horiz-adv-x="77.7832" d="M16.4062 36.5312 Q21.2969 41.4062 22.1719 42.1406 Q24.3594 44 26.8984 45.0234 Q29.4375 46.0469 31.9375 46.0469 Q36.1406 46.0469 39.1641 43.6016 Q42.1875 41.1562 43.2188 36.5312 Q48.25 42.3906 51.7109 44.2188 Q55.1719 46.0469 58.8438 46.0469 Q62.4062 46.0469 65.1641 44.2188 Q67.9219 42.3906 69.5312 38.2344 Q70.6094 35.4062 70.6094 29.3438 L70.6094 10.1094 Q70.6094 5.9062 71.2344 4.3438 Q71.7344 3.2656 73.0469 2.5156 Q74.3594 1.7656 77.3438 1.7656 L77.3438 0 L55.2812 0 L55.2812 1.7656 L56.2031 1.7656 Q59.0781 1.7656 60.6875 2.875 Q61.8125 3.6562 62.3125 5.375 Q62.5 6.2031 62.5 10.1094 L62.5 29.3438 Q62.5 34.8125 61.1875 37.0625 Q59.2812 40.1875 55.0781 40.1875 Q52.4844 40.1875 49.875 38.8906 Q47.2656 37.5938 43.5625 34.0781 L43.4531 33.5469 L43.5625 31.4531 L43.5625 10.1094 Q43.5625 5.5156 44.0703 4.3906 Q44.5781 3.2656 45.9922 2.5156 Q47.4062 1.7656 50.8281 1.7656 L50.8281 0 L28.2188 0 L28.2188 1.7656 Q31.9375 1.7656 33.3281 2.6406 Q34.7188 3.5156 35.25 5.2812 Q35.5 6.1094 35.5 10.1094 L35.5 29.3438 Q35.5 34.8125 33.8906 37.2031 Q31.7344 40.3281 27.875 40.3281 Q25.25 40.3281 22.6562 38.9219 Q18.6094 36.7656 16.4062 34.0781 L16.4062 10.1094 Q16.4062 5.7188 17.0156 4.3984 Q17.625 3.0781 18.8203 2.4219 Q20.0156 1.7656 23.6875 1.7656 L23.6875 0 L1.5625 0 L1.5625 1.7656 Q4.6406 1.7656 5.8594 2.4219 Q7.0781 3.0781 7.7109 4.5156 Q8.3438 5.9531 8.3438 10.1094 L8.3438 27.2031 Q8.3438 34.5781 7.9062 36.7188 Q7.5625 38.3281 6.8359 38.9375 Q6.1094 39.5469 4.8281 39.5469 Q3.4688 39.5469 1.5625 38.8125 L0.8281 40.5781 L14.3125 46.0469 L16.4062 46.0469 L16.4062 36.5312 Z"
        /><glyph unicode="i" horiz-adv-x="27.783203" d="M14.5 69.4375 Q16.5469 69.4375 17.9922 67.9922 Q19.4375 66.5469 19.4375 64.5 Q19.4375 62.4531 17.9922 60.9844 Q16.5469 59.5156 14.5 59.5156 Q12.4531 59.5156 10.9844 60.9844 Q9.5156 62.4531 9.5156 64.5 Q9.5156 66.5469 10.9609 67.9922 Q12.4062 69.4375 14.5 69.4375 ZM18.5625 46.0469 L18.5625 10.1094 Q18.5625 5.9062 19.1719 4.5156 Q19.7812 3.125 20.9766 2.4453 Q22.1719 1.7656 25.3438 1.7656 L25.3438 0 L3.6094 0 L3.6094 1.7656 Q6.8906 1.7656 8.0078 2.3984 Q9.125 3.0312 9.7891 4.4922 Q10.4531 5.9531 10.4531 10.1094 L10.4531 27.3438 Q10.4531 34.625 10.0156 36.7656 Q9.6719 38.3281 8.9375 38.9375 Q8.2031 39.5469 6.9375 39.5469 Q5.5625 39.5469 3.6094 38.8125 L2.9375 40.5781 L16.4062 46.0469 L18.5625 46.0469 Z"
        /><glyph unicode="T" horiz-adv-x="61.083984" d="M57.8594 66.2188 L58.5938 50.6875 L56.7344 50.6875 Q56.2031 54.7812 55.2812 56.5469 Q53.7656 59.375 51.25 60.7188 Q48.7344 62.0625 44.625 62.0625 L35.2969 62.0625 L35.2969 11.4688 Q35.2969 5.375 36.625 3.8594 Q38.4844 1.8125 42.3281 1.8125 L44.625 1.8125 L44.625 0 L16.5469 0 L16.5469 1.8125 L18.8906 1.8125 Q23.0938 1.8125 24.8594 4.3438 Q25.9219 5.9062 25.9219 11.4688 L25.9219 62.0625 L17.9688 62.0625 Q13.3281 62.0625 11.375 61.375 Q8.8438 60.4531 7.0312 57.8125 Q5.2188 55.1719 4.8906 50.6875 L3.0312 50.6875 L3.8125 66.2188 L57.8594 66.2188 Z"
        /><glyph unicode="B" horiz-adv-x="66.69922" d="M46.1875 33.7969 Q53.0781 32.3281 56.5 29.1094 Q61.2344 24.6094 61.2344 18.1094 Q61.2344 13.1875 58.1094 8.6719 Q54.9844 4.1562 49.5391 2.0781 Q44.0938 0 32.9062 0 L1.6562 0 L1.6562 1.8125 L4.1562 1.8125 Q8.2969 1.8125 10.1094 4.4375 Q11.2344 6.1562 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.6406 9.8125 62.25 Q7.9062 64.4062 4.1562 64.4062 L1.6562 64.4062 L1.6562 66.2188 L30.2812 66.2188 Q38.2812 66.2188 43.1094 65.0469 Q50.4375 63.2812 54.2969 58.8125 Q58.1562 54.3438 58.1562 48.5312 Q58.1562 43.5625 55.125 39.625 Q52.0938 35.6875 46.1875 33.7969 ZM20.6094 36.4219 Q22.4062 36.0781 24.7266 35.9141 Q27.0469 35.75 29.8281 35.75 Q36.9688 35.75 40.5547 37.2812 Q44.1406 38.8125 46.0469 41.9922 Q47.9531 45.1719 47.9531 48.9219 Q47.9531 54.7344 43.2188 58.8359 Q38.4844 62.9375 29.3906 62.9375 Q24.5156 62.9375 20.6094 61.8594 L20.6094 36.4219 ZM20.6094 4.7812 Q26.2656 3.4688 31.7812 3.4688 Q40.625 3.4688 45.2656 7.4453 Q49.9062 11.4219 49.9062 17.2812 Q49.9062 21.1406 47.8047 24.7031 Q45.7031 28.2656 40.9688 30.3203 Q36.2344 32.375 29.25 32.375 Q26.2188 32.375 24.0703 32.2734 Q21.9219 32.1719 20.6094 31.9375 L20.6094 4.7812 Z"
        /><glyph unicode="d" horiz-adv-x="50.0" d="M34.7188 5.0312 Q31.4531 1.6094 28.3281 0.1172 Q25.2031 -1.375 21.5781 -1.375 Q14.2656 -1.375 8.7969 4.7578 Q3.3281 10.8906 3.3281 20.5156 Q3.3281 30.125 9.3828 38.1094 Q15.4375 46.0938 24.9531 46.0938 Q30.8594 46.0938 34.7188 42.3281 L34.7188 50.5938 Q34.7188 58.25 34.3516 60.0078 Q33.9844 61.7656 33.2031 62.3984 Q32.4219 63.0312 31.25 63.0312 Q29.9844 63.0312 27.875 62.25 L27.25 63.9688 L40.5781 69.4375 L42.7812 69.4375 L42.7812 17.7188 Q42.7812 9.8594 43.1406 8.125 Q43.5 6.3906 44.3125 5.7109 Q45.125 5.0312 46.1875 5.0312 Q47.5156 5.0312 49.7031 5.8594 L50.25 4.1562 L36.9688 -1.375 L34.7188 -1.375 L34.7188 5.0312 ZM34.7188 8.4531 L34.7188 31.5 Q34.4219 34.8125 32.9609 37.5469 Q31.5 40.2812 29.0781 41.6719 Q26.6562 43.0625 24.3594 43.0625 Q20.0625 43.0625 16.7031 39.2031 Q12.25 34.125 12.25 24.3594 Q12.25 14.5 16.5469 9.25 Q20.8438 4 26.125 4 Q30.5625 4 34.7188 8.4531 Z"
        /><glyph unicode="R" horiz-adv-x="66.69922" d="M67.5781 0 L49.9062 0 L27.4844 30.9531 Q25 30.8594 23.4375 30.8594 Q22.7969 30.8594 22.0703 30.8828 Q21.3438 30.9062 20.5625 30.9531 L20.5625 11.7188 Q20.5625 5.4688 21.9219 3.9531 Q23.7812 1.8125 27.4844 1.8125 L30.0781 1.8125 L30.0781 0 L1.7031 0 L1.7031 1.8125 L4.2031 1.8125 Q8.4062 1.8125 10.2031 4.5469 Q11.2344 6.0625 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.75 9.8594 62.25 Q7.9531 64.4062 4.2031 64.4062 L1.7031 64.4062 L1.7031 66.2188 L25.8281 66.2188 Q36.375 66.2188 41.3828 64.6797 Q46.3906 63.1406 49.8828 59.0156 Q53.375 54.8906 53.375 49.1719 Q53.375 43.0625 49.3906 38.5703 Q45.4062 34.0781 37.0625 32.2344 L50.7344 13.2344 Q55.4219 6.6875 58.7891 4.5391 Q62.1562 2.3906 67.5781 1.8125 L67.5781 0 ZM20.5625 34.0312 Q21.4844 34.0312 22.1719 34.0078 Q22.8594 33.9844 23.2969 33.9844 Q32.7656 33.9844 37.5781 38.0859 Q42.3906 42.1875 42.3906 48.5312 Q42.3906 54.7344 38.5078 58.6172 Q34.625 62.5 28.2188 62.5 Q25.3906 62.5 20.5625 61.5781 L20.5625 34.0312 Z"
        /><glyph unicode="N" horiz-adv-x="72.2168" d="M-1.3125 66.2188 L16.6562 66.2188 L57.125 16.5469 L57.125 54.7344 Q57.125 60.8438 55.7656 62.3594 Q53.9531 64.4062 50.0469 64.4062 L47.75 64.4062 L47.75 66.2188 L70.7969 66.2188 L70.7969 64.4062 L68.4531 64.4062 Q64.2656 64.4062 62.5 61.8594 Q61.4219 60.2969 61.4219 54.7344 L61.4219 -1.0781 L59.6719 -1.0781 L16.0156 52.25 L16.0156 11.4688 Q16.0156 5.375 17.3281 3.8594 Q19.1875 1.8125 23.0469 1.8125 L25.3906 1.8125 L25.3906 0 L2.3438 0 L2.3438 1.8125 L4.6406 1.8125 Q8.8906 1.8125 10.6406 4.3438 Q11.7188 5.9062 11.7188 11.4688 L11.7188 57.5156 Q8.8438 60.8906 7.3516 61.9609 Q5.8594 63.0312 2.9844 63.9688 Q1.5625 64.4062 -1.3125 64.4062 L-1.3125 66.2188 Z"
        /><glyph unicode="S" horiz-adv-x="55.615234" d="M45.8438 67.7188 L45.8438 44.8281 L44.0469 44.8281 Q43.1719 51.4219 40.8984 55.3281 Q38.625 59.2344 34.4219 61.5234 Q30.2188 63.8125 25.7344 63.8125 Q20.6562 63.8125 17.3359 60.7188 Q14.0156 57.625 14.0156 53.6562 Q14.0156 50.6406 16.1094 48.1406 Q19.1406 44.4844 30.5156 38.375 Q39.7969 33.4062 43.1875 30.7422 Q46.5781 28.0781 48.4141 24.4609 Q50.25 20.8438 50.25 16.8906 Q50.25 9.375 44.4141 3.9297 Q38.5781 -1.5156 29.3906 -1.5156 Q26.5156 -1.5156 23.9688 -1.0781 Q22.4688 -0.8281 17.7031 0.7109 Q12.9375 2.25 11.6719 2.25 Q10.4531 2.25 9.7422 1.5156 Q9.0312 0.7812 8.6875 -1.5156 L6.8906 -1.5156 L6.8906 21.1875 L8.6875 21.1875 Q9.9688 14.0625 12.1172 10.5234 Q14.2656 6.9844 18.6797 4.6406 Q23.0938 2.2969 28.375 2.2969 Q34.4688 2.2969 38.0078 5.5156 Q41.5469 8.7344 41.5469 13.1406 Q41.5469 15.5781 40.2109 18.0703 Q38.875 20.5625 36.0312 22.7031 Q34.125 24.1719 25.6328 28.9297 Q17.1406 33.6875 13.5547 36.5234 Q9.9688 39.3594 8.1094 42.7734 Q6.25 46.1875 6.25 50.2969 Q6.25 57.4219 11.7188 62.5703 Q17.1875 67.7188 25.6406 67.7188 Q30.9062 67.7188 36.8125 65.1406 Q39.5469 63.9219 40.6719 63.9219 Q41.9375 63.9219 42.75 64.6797 Q43.5625 65.4375 44.0469 67.7188 L45.8438 67.7188 Z"
      /></font
    ></defs
    ><g style="fill:white; stroke:white;"
    ><rect x="0" y="0" width="1800" style="clip-path:url(#clipPath1); stroke:none;" height="1200"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><rect x="0" width="1800" height="1200" y="0" style="stroke:none;"
      /><path style="stroke:none;" d="M234 1068 L1629 1068 L1629 90 L234 90 Z"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="89.9992" style="fill:none;" x1="280.5" x2="280.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="327" x2="327" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="373.5" x2="373.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="420" x2="420" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="513" x2="513" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="559.5" x2="559.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="606" x2="606" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="652.5" x2="652.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="745.5" x2="745.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="792" x2="792" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="838.5" x2="838.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="885" x2="885" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="977.9999" x2="977.9999" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1024.5" x2="1024.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1071" x2="1071" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1117.5" x2="1117.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1210.5" x2="1210.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1257" x2="1257" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1303.5" x2="1303.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1350" x2="1350" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1443" x2="1443" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1489.5" x2="1489.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1536" x2="1536" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1582.5" x2="1582.5" y1="1068.0009"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="89.9992" style="fill:none;" x1="234" x2="234" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="466.5" x2="466.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="699" x2="699" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="931.5" x2="931.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1164" x2="1164" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1396.5" x2="1396.5" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1629" x2="1629" y1="1068.0009"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="1046.267" style="fill:none;" x1="1629" x2="234" y1="1046.267"
      /><line y2="1024.5332" style="fill:none;" x1="1629" x2="234" y1="1024.5332"
      /><line y2="1002.7993" style="fill:none;" x1="1629" x2="234" y1="1002.7993"
      /><line y2="981.0655" style="fill:none;" x1="1629" x2="234" y1="981.0655"
      /><line y2="937.602" style="fill:none;" x1="1629" x2="234" y1="937.602"
      /><line y2="915.8682" style="fill:none;" x1="1629" x2="234" y1="915.8682"
      /><line y2="894.1343" style="fill:none;" x1="1629" x2="234" y1="894.1343"
      /><line y2="872.4005" style="fill:none;" x1="1629" x2="234" y1="872.4005"
      /><line y2="828.9329" style="fill:none;" x1="1629" x2="234" y1="828.9329"
      /><line y2="807.199" style="fill:none;" x1="1629" x2="234" y1="807.199"
      /><line y2="785.4651" style="fill:none;" x1="1629" x2="234" y1="785.4651"
      /><line y2="763.7313" style="fill:none;" x1="1629" x2="234" y1="763.7313"
      /><line y2="720.2678" style="fill:none;" x1="1629" x2="234" y1="720.2678"
      /><line y2="698.534" style="fill:none;" x1="1629" x2="234" y1="698.534"
      /><line y2="676.8002" style="fill:none;" x1="1629" x2="234" y1="676.8002"
      /><line y2="655.0663" style="fill:none;" x1="1629" x2="234" y1="655.0663"
      /><line y2="611.5987" style="fill:none;" x1="1629" x2="234" y1="611.5987"
      /><line y2="589.8649" style="fill:none;" x1="1629" x2="234" y1="589.8649"
      /><line y2="568.1351" style="fill:none;" x1="1629" x2="234" y1="568.1351"
      /><line y2="546.4013" style="fill:none;" x1="1629" x2="234" y1="546.4013"
      /><line y2="502.9337" style="fill:none;" x1="1629" x2="234" y1="502.9337"
      /><line y2="481.1998" style="fill:none;" x1="1629" x2="234" y1="481.1998"
      /><line y2="459.466" style="fill:none;" x1="1629" x2="234" y1="459.466"
      /><line y2="437.7322" style="fill:none;" x1="1629" x2="234" y1="437.7322"
      /><line y2="394.2686" style="fill:none;" x1="1629" x2="234" y1="394.2686"
      /><line y2="372.5348" style="fill:none;" x1="1629" x2="234" y1="372.5348"
      /><line y2="350.801" style="fill:none;" x1="1629" x2="234" y1="350.801"
      /><line y2="329.0672" style="fill:none;" x1="1629" x2="234" y1="329.0672"
      /><line y2="285.5995" style="fill:none;" x1="1629" x2="234" y1="285.5995"
      /><line y2="263.8657" style="fill:none;" x1="1629" x2="234" y1="263.8657"
      /><line y2="242.1318" style="fill:none;" x1="1629" x2="234" y1="242.1318"
      /><line y2="220.398" style="fill:none;" x1="1629" x2="234" y1="220.398"
      /><line y2="176.9345" style="fill:none;" x1="1629" x2="234" y1="176.9345"
      /><line y2="155.2007" style="fill:none;" x1="1629" x2="234" y1="155.2007"
      /><line y2="133.4668" style="fill:none;" x1="1629" x2="234" y1="133.4668"
      /><line y2="111.733" style="fill:none;" x1="1629" x2="234" y1="111.733"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="1068.0009" style="fill:none;" x1="1629" x2="234" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="1629" x2="234" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="1629" x2="234" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="1629" x2="234" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="1629" x2="234" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="1629" x2="234" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="1629" x2="234" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="1629" x2="234" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="1629" x2="234" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="1629" x2="234" y1="89.9992"
      /><line x1="234" x2="1629" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1068.0009"
      /><line x1="234" x2="1629" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="89.9992"
      /><line x1="234" x2="234" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="466.5" x2="466.5" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="699" x2="699" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="931.5" x2="931.5" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="1164" x2="1164" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="1396.5" x2="1396.5" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="1629" x2="1629" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0519"
      /><line x1="234" x2="234" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
      /><line x1="466.5" x2="466.5" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
      /><line x1="699" x2="699" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
      /><line x1="931.5" x2="931.5" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
      /><line x1="1164" x2="1164" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
      /><line x1="1396.5" x2="1396.5" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
      /><line x1="1629" x2="1629" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9481"
    /></g
    ><g transform="translate(234,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >1</text
    ></g
    ><g transform="translate(466.5,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >2</text
    ></g
    ><g transform="translate(699,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >3</text
    ></g
    ><g transform="translate(931.5,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >4</text
    ></g
    ><g transform="translate(1164,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >5</text
    ></g
    ><g transform="translate(1396.5,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >6</text
    ></g
    ><g transform="translate(1629,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >7</text
    ></g
    ><g transform="translate(931.5007,1114.3329)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-85.5" xml:space="preserve" y="35" style="stroke:none;"
      >Time (min)</text
    ></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><line y2="89.9992" style="fill:none;" x1="234" x2="234" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1629" x2="1629" y1="1068.0009"
      /><line y2="1068.0009" style="fill:none;" x1="234" x2="247.95" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="234" x2="247.95" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="234" x2="247.95" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="234" x2="247.95" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="234" x2="247.95" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="234" x2="247.95" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="234" x2="247.95" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="234" x2="247.95" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="234" x2="247.95" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="234" x2="247.95" y1="89.9992"
      /><line y2="1068.0009" style="fill:none;" x1="1629" x2="1615.05" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="1629" x2="1615.05" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="1629" x2="1615.05" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="1629" x2="1615.05" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="1629" x2="1615.05" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="1629" x2="1615.05" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="1629" x2="1615.05" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="1629" x2="1615.05" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="1629" x2="1615.05" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="1629" x2="1615.05" y1="89.9992"
    /></g
    ><g transform="translate(223.3333,1068.0009)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.4</text
    ></g
    ><g transform="translate(223.3333,959.3317)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.45</text
    ></g
    ><g transform="translate(223.3333,850.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.5</text
    ></g
    ><g transform="translate(223.3333,742.0016)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.55</text
    ></g
    ><g transform="translate(223.3333,633.3325)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.6</text
    ></g
    ><g transform="translate(223.3333,524.6675)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.65</text
    ></g
    ><g transform="translate(223.3333,415.9984)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.7</text
    ></g
    ><g transform="translate(223.3333,307.3333)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.75</text
    ></g
    ><g transform="translate(223.3333,198.6683)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.8</text
    ></g
    ><g transform="translate(223.3333,89.9992)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.85</text
    ></g
    ><g transform="translate(148.3333,579) rotate(-90)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-75.5" xml:space="preserve" y="-8" style="stroke:none;"
      >SNR (dB)</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><path d="M234 183.6706 L466.5 161.7212 L699 179.1066 L931.5 257.3451 L1164 236.7015 L1396.5 263.8657 L1629 268.2141" style="fill:none; fill-rule:evenodd;"
    /></g
    ><g transform="translate(234,183.6706)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(234,183.6706)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,161.7212)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,161.7212)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,179.1066)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,179.1066)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,257.3451)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,257.3451)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,236.7015)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,236.7015)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,263.8657)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,263.8657)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,268.2141)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,268.2141)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1629,-268.2141)" style="fill:none; fill-rule:evenodd; stroke:rgb(217,83,25); stroke-linejoin:round;" d="M234 350.801 L466.5 320.3745 L699 448.6012 L931.5 494.241 L1164 392.0924 L1396.5 422.5189 L1629 370.3586"
    /></g
    ><g transform="translate(234,350.801)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(234,350.801)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,320.3745)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,320.3745)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,448.6012)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,448.6012)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,494.241)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,494.241)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,392.0924)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,392.0924)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,422.5189)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,422.5189)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,370.3586)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,370.3586)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1629,-370.3586)" style="fill:none; fill-rule:evenodd; stroke:rgb(237,177,32); stroke-linejoin:round;" d="M234 511.6264 L466.5 529.0118 L699 546.4013 L931.5 692.0134 L1164 539.8807 L1396.5 518.1469 L1629 548.5735"
    /></g
    ><g transform="translate(234,511.6264)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(234,511.6264)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,529.0118)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,529.0118)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,546.4013)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,546.4013)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,692.0134)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,692.0134)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,539.8807)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,539.8807)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,518.1469)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,518.1469)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,548.5735)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,548.5735)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1629,-548.5735)" style="fill:none; fill-rule:evenodd; stroke:rgb(126,47,142); stroke-linejoin:round;" d="M234 781.1209 L466.5 715.9194 L699 1018.0126 L931.5 881.0932 L1164 692.0134 L1396.5 628.9882 L1629 722.4399"
    /></g
    ><g transform="translate(234,781.1209)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(234,781.1209)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,715.9194)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(466.5,715.9194)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,1018.0126)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(699,1018.0126)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,881.0932)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(931.5,881.0932)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,692.0134)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1164,692.0134)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,628.9882)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1396.5,628.9882)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,722.4399)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1629,722.4399)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><path style="stroke:none;" d="M1610 1038 L1610 998 L892 998 L892 1038 Z"
    /></g
    ><g transform="translate(986,1017.5)" style="text-rendering:geometricPrecision; font-size:24px; color-interpolation:linearRGB; color-rendering:optimizeQuality; image-rendering:optimizeQuality;"
    ><text x="0" xml:space="preserve" y="9.5" style="stroke:none;"
      >1 kHz</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1017.5" style="fill:none;" x1="900" x2="980" y1="1017.5"
    /></g
    ><g transform="translate(940,1017.5)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(940,1017.5)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1144,1017.5)" style="text-rendering:geometricPrecision; font-size:24px; color-interpolation:linearRGB; color-rendering:optimizeQuality; image-rendering:optimizeQuality;"
    ><text x="0" xml:space="preserve" y="9.5" style="stroke:none;"
      >10 kHz</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1017.5" style="fill:none;" x1="1058" x2="1138" y1="1017.5"
    /></g
    ><g transform="translate(1098,1017.5)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1098,1017.5)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1315,1017.5)" style="text-rendering:geometricPrecision; font-size:24px; color-interpolation:linearRGB; color-rendering:optimizeQuality; image-rendering:optimizeQuality;"
    ><text x="0" xml:space="preserve" y="9.5" style="stroke:none;"
      >100 kHz</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1017.5" style="fill:none;" x1="1229" x2="1309" y1="1017.5"
    /></g
    ><g transform="translate(1269,1017.5)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1269,1017.5)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1500,1017.5)" style="text-rendering:geometricPrecision; font-size:24px; color-interpolation:linearRGB; color-rendering:optimizeQuality; image-rendering:optimizeQuality;"
    ><text x="0" xml:space="preserve" y="9.5" style="stroke:none;"
      >1000 kHz</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1017.5" style="fill:none;" x1="1414" x2="1494" y1="1017.5"
    /></g
    ><g transform="translate(1454,1017.5)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1454,1017.5)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1454,-1017.5)" style="fill:none; stroke-width:1.3333; fill-rule:evenodd; stroke:rgb(38,38,38);" d="M892 1038 L892 998 L1610 998 L1610 1038 Z"
    /></g
  ></g
></svg
>
