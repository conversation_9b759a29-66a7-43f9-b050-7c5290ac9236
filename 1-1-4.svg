<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.0//EN'
          'http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd'>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" style="fill-opacity:1; color-rendering:auto; color-interpolation:auto; text-rendering:auto; stroke:black; stroke-linecap:square; stroke-miterlimit:10; shape-rendering:auto; stroke-opacity:1; fill:black; stroke-dasharray:none; font-weight:normal; stroke-width:1; font-family:'Dialog'; font-style:normal; stroke-linejoin:miter; font-size:12px; stroke-dashoffset:0; image-rendering:auto;" width="900" height="600" xmlns="http://www.w3.org/2000/svg"
><!--Generated by the Batik Graphics2D SVG Generator--><defs id="genericDefs"
  /><g
  ><defs id="defs1"
    ><clipPath clipPathUnits="userSpaceOnUse" id="clipPath1"
      ><path d="M0 0 L900 0 L900 600 L0 600 L0 0 Z"
      /></clipPath
      ><font horiz-adv-x="75.0" id="font1"
      ><font-face ascent="100.53711" descent="21.972656" units-per-em="100" style="font-style:normal; font-family:Dialog; font-weight:normal;"
        /><missing-glyph horiz-adv-x="75.0" d="M12.5 0 L12.5 62.5 L62.5 62.5 L62.5 0 L12.5 0 ZM14.0625 1.5625 L60.9375 1.5625 L60.9375 60.9375 L14.0625 60.9375 L14.0625 1.5625 Z"
        /><glyph unicode="0" horiz-adv-x="55.615234" d="M4.1562 35.2969 Q4.1562 48 6.7656 55.7422 Q9.375 63.4844 14.5234 67.6797 Q19.6719 71.875 27.4844 71.875 Q33.25 71.875 37.5938 69.5547 Q41.9375 67.2344 44.7734 62.8672 Q47.6094 58.5 49.2188 52.2266 Q50.8281 45.9531 50.8281 35.2969 Q50.8281 22.7031 48.2422 14.9688 Q45.6562 7.2344 40.5078 3.0078 Q35.3594 -1.2188 27.4844 -1.2188 Q17.1406 -1.2188 11.2344 6.2031 Q4.1562 15.1406 4.1562 35.2969 ZM13.1875 35.2969 Q13.1875 17.6719 17.3125 11.8359 Q21.4375 6 27.4844 6 Q33.5469 6 37.6719 11.8594 Q41.7969 17.7188 41.7969 35.2969 Q41.7969 52.9844 37.6719 58.7891 Q33.5469 64.5938 27.3906 64.5938 Q21.3438 64.5938 17.7188 59.4688 Q13.1875 52.9375 13.1875 35.2969 Z"
        /><glyph unicode="3" horiz-adv-x="55.615234" d="M4.2031 18.8906 L12.9844 20.0625 Q14.5 12.5938 18.1406 9.2969 Q21.7812 6 27 6 Q33.2031 6 37.4766 10.2969 Q41.75 14.5938 41.75 20.9531 Q41.75 27 37.7969 30.9297 Q33.8438 34.8594 27.7344 34.8594 Q25.25 34.8594 21.5312 33.8906 L22.5156 41.6094 Q23.3906 41.5 23.9219 41.5 Q29.5469 41.5 34.0391 44.4297 Q38.5312 47.3594 38.5312 53.4688 Q38.5312 58.2969 35.2578 61.4766 Q31.9844 64.6562 26.8125 64.6562 Q21.6875 64.6562 18.2656 61.4297 Q14.8438 58.2031 13.875 51.7656 L5.0781 53.3281 Q6.6875 62.1562 12.3984 67.0156 Q18.1094 71.875 26.6094 71.875 Q32.4688 71.875 37.3984 69.3594 Q42.3281 66.8438 44.9453 62.5 Q47.5625 58.1562 47.5625 53.2656 Q47.5625 48.6406 45.0703 44.8281 Q42.5781 41.0156 37.7031 38.7656 Q44.0469 37.3125 47.5625 32.6953 Q51.0781 28.0781 51.0781 21.1406 Q51.0781 11.7656 44.2422 5.25 Q37.4062 -1.2656 26.9531 -1.2656 Q17.5312 -1.2656 11.3047 4.3516 Q5.0781 9.9688 4.2031 18.8906 Z"
        /><glyph unicode="5" horiz-adv-x="55.615234" d="M4.1562 18.75 L13.375 19.5312 Q14.4062 12.7969 18.1406 9.3984 Q21.875 6 27.1562 6 Q33.5 6 37.8906 10.7891 Q42.2812 15.5781 42.2812 23.4844 Q42.2812 31 38.0625 35.3516 Q33.8438 39.7031 27 39.7031 Q22.75 39.7031 19.3359 37.7734 Q15.9219 35.8438 13.9688 32.7656 L5.7188 33.8438 L12.6406 70.6094 L48.25 70.6094 L48.25 62.2031 L19.6719 62.2031 L15.8281 42.9688 Q22.2656 47.4688 29.3438 47.4688 Q38.7188 47.4688 45.1641 40.9688 Q51.6094 34.4688 51.6094 24.2656 Q51.6094 14.5469 45.9531 7.4688 Q39.0625 -1.2188 27.1562 -1.2188 Q17.3906 -1.2188 11.2109 4.25 Q5.0312 9.7188 4.1562 18.75 Z"
        /><glyph unicode="1" horiz-adv-x="55.615234" d="M37.25 0 L28.4688 0 L28.4688 56 Q25.2969 52.9844 20.1406 49.9531 Q14.9844 46.9219 10.8906 45.4062 L10.8906 53.9062 Q18.2656 57.375 23.7812 62.3047 Q29.2969 67.2344 31.5938 71.875 L37.25 71.875 L37.25 0 Z"
        /><glyph unicode="2" horiz-adv-x="55.615234" d="M50.3438 8.4531 L50.3438 0 L3.0312 0 Q2.9375 3.1719 4.0469 6.1094 Q5.8594 10.9375 9.8359 15.625 Q13.8125 20.3125 21.3438 26.4688 Q33.0156 36.0312 37.1172 41.625 Q41.2188 47.2188 41.2188 52.2031 Q41.2188 57.4219 37.4766 61.0078 Q33.7344 64.5938 27.7344 64.5938 Q21.3906 64.5938 17.5781 60.7891 Q13.7656 56.9844 13.7188 50.25 L4.6875 51.1719 Q5.6094 61.2812 11.6641 66.5781 Q17.7188 71.875 27.9375 71.875 Q38.2344 71.875 44.2422 66.1641 Q50.25 60.4531 50.25 52 Q50.25 47.7031 48.4922 43.5547 Q46.7344 39.4062 42.6562 34.8125 Q38.5781 30.2188 29.1094 22.2188 Q21.1875 15.5781 18.9453 13.2109 Q16.7031 10.8438 15.2344 8.4531 L50.3438 8.4531 Z"
        /><glyph unicode="8" horiz-adv-x="55.615234" d="M17.6719 38.8125 Q12.2031 40.8281 9.5703 44.5391 Q6.9375 48.25 6.9375 53.4219 Q6.9375 61.2344 12.5547 66.5547 Q18.1719 71.875 27.4844 71.875 Q36.8594 71.875 42.5781 66.4297 Q48.2969 60.9844 48.2969 53.1719 Q48.2969 48.1875 45.6797 44.5078 Q43.0625 40.8281 37.75 38.8125 Q44.3438 36.6719 47.7812 31.8828 Q51.2188 27.0938 51.2188 20.4531 Q51.2188 11.2812 44.7266 5.0312 Q38.2344 -1.2188 27.6406 -1.2188 Q17.0469 -1.2188 10.5469 5.0547 Q4.0469 11.3281 4.0469 20.7031 Q4.0469 27.6875 7.5938 32.3984 Q11.1406 37.1094 17.6719 38.8125 ZM15.9219 53.7188 Q15.9219 48.6406 19.1953 45.4141 Q22.4688 42.1875 27.6875 42.1875 Q32.7656 42.1875 36.0156 45.3828 Q39.2656 48.5781 39.2656 53.2188 Q39.2656 58.0625 35.9141 61.3594 Q32.5625 64.6562 27.5938 64.6562 Q22.5625 64.6562 19.2422 61.4297 Q15.9219 58.2031 15.9219 53.7188 ZM13.0938 20.6562 Q13.0938 16.8906 14.875 13.375 Q16.6562 9.8594 20.1719 7.9297 Q23.6875 6 27.7344 6 Q34.0312 6 38.1328 10.0547 Q42.2344 14.1094 42.2344 20.3594 Q42.2344 26.7031 38.0156 30.8594 Q33.7969 35.0156 27.4375 35.0156 Q21.2344 35.0156 17.1641 30.9141 Q13.0938 26.8125 13.0938 20.6562 Z"
        /><glyph unicode="." horiz-adv-x="27.783203" d="M9.0781 0 L9.0781 10.0156 L19.0938 10.0156 L19.0938 0 L9.0781 0 Z"
        /><glyph unicode="6" horiz-adv-x="55.615234" d="M49.75 54.0469 L41.0156 53.375 Q39.8438 58.5469 37.7031 60.8906 Q34.125 64.6562 28.9062 64.6562 Q24.7031 64.6562 21.5312 62.3125 Q17.3906 59.2812 14.9922 53.4688 Q12.5938 47.6562 12.5 36.9219 Q15.6719 41.75 20.2656 44.0938 Q24.8594 46.4375 29.8906 46.4375 Q38.6719 46.4375 44.8516 39.9688 Q51.0312 33.5 51.0312 23.25 Q51.0312 16.5 48.125 10.7188 Q45.2188 4.9375 40.1406 1.8594 Q35.0625 -1.2188 28.6094 -1.2188 Q17.625 -1.2188 10.6953 6.8594 Q3.7656 14.9375 3.7656 33.5 Q3.7656 54.25 11.4219 63.6719 Q18.1094 71.875 29.4375 71.875 Q37.8906 71.875 43.2891 67.1406 Q48.6875 62.4062 49.75 54.0469 ZM13.875 23.1875 Q13.875 18.6562 15.7969 14.5078 Q17.7188 10.3594 21.1875 8.1797 Q24.6562 6 28.4688 6 Q34.0312 6 38.0391 10.4922 Q42.0469 14.9844 42.0469 22.7031 Q42.0469 30.125 38.0859 34.3984 Q34.125 38.6719 28.125 38.6719 Q22.1719 38.6719 18.0234 34.3984 Q13.875 30.125 13.875 23.1875 Z"
        /><glyph unicode="9" horiz-adv-x="55.615234" d="M5.4688 16.5469 L13.9219 17.3281 Q14.9844 11.375 18.0156 8.6875 Q21.0469 6 25.7812 6 Q29.8281 6 32.8828 7.8594 Q35.9375 9.7188 37.8906 12.8203 Q39.8438 15.9219 41.1641 21.1953 Q42.4844 26.4688 42.4844 31.9375 Q42.4844 32.5156 42.4375 33.6875 Q39.7969 29.5 35.2344 26.8828 Q30.6719 24.2656 25.3438 24.2656 Q16.4531 24.2656 10.3047 30.7109 Q4.1562 37.1562 4.1562 47.7031 Q4.1562 58.5938 10.5781 65.2344 Q17 71.875 26.6562 71.875 Q33.6406 71.875 39.4297 68.1172 Q45.2188 64.3594 48.2188 57.3984 Q51.2188 50.4375 51.2188 37.25 Q51.2188 23.5312 48.2422 15.4062 Q45.2656 7.2812 39.3828 3.0312 Q33.5 -1.2188 25.5938 -1.2188 Q17.1875 -1.2188 11.8672 3.4453 Q6.5469 8.1094 5.4688 16.5469 ZM41.4531 48.1406 Q41.4531 55.7188 37.4297 60.1562 Q33.4062 64.5938 27.7344 64.5938 Q21.875 64.5938 17.5312 59.8125 Q13.1875 55.0312 13.1875 47.4062 Q13.1875 40.5781 17.3125 36.3047 Q21.4375 32.0312 27.4844 32.0312 Q33.5938 32.0312 37.5234 36.3047 Q41.4531 40.5781 41.4531 48.1406 Z"
        /><glyph unicode="7" horiz-adv-x="55.615234" d="M4.7344 62.2031 L4.7344 70.6562 L51.0781 70.6562 L51.0781 63.8125 Q44.2344 56.5469 37.5234 44.4844 Q30.8125 32.4219 27.1562 19.6719 Q24.5156 10.6875 23.7812 0 L14.75 0 Q14.8906 8.4531 18.0625 20.4141 Q21.2344 32.375 27.1719 43.4844 Q33.1094 54.5938 39.7969 62.2031 L4.7344 62.2031 Z"
        /><glyph unicode="4" horiz-adv-x="55.615234" d="M32.3281 0 L32.3281 17.1406 L1.2656 17.1406 L1.2656 25.2031 L33.9375 71.5781 L41.1094 71.5781 L41.1094 25.2031 L50.7812 25.2031 L50.7812 17.1406 L41.1094 17.1406 L41.1094 0 L32.3281 0 ZM32.3281 25.2031 L32.3281 57.4688 L9.9062 25.2031 L32.3281 25.2031 Z"
        /><glyph unicode="u" horiz-adv-x="55.615234" d="M40.5781 0 L40.5781 7.625 Q34.5156 -1.1719 24.125 -1.1719 Q19.5312 -1.1719 15.5547 0.5859 Q11.5781 2.3438 9.6484 5.0078 Q7.7188 7.6719 6.9375 11.5312 Q6.3906 14.1094 6.3906 19.7344 L6.3906 51.8594 L15.1875 51.8594 L15.1875 23.0938 Q15.1875 16.2188 15.7188 13.8125 Q16.5469 10.3594 19.2344 8.375 Q21.9219 6.3906 25.875 6.3906 Q29.8281 6.3906 33.2969 8.4219 Q36.7656 10.4531 38.2109 13.9453 Q39.6562 17.4375 39.6562 24.0781 L39.6562 51.8594 L48.4375 51.8594 L48.4375 0 L40.5781 0 Z"
        /><glyph unicode="c" horiz-adv-x="50.0" d="M40.4375 19 L49.0781 17.875 Q47.6562 8.9375 41.8203 3.8828 Q35.9844 -1.1719 27.4844 -1.1719 Q16.8438 -1.1719 10.375 5.7891 Q3.9062 12.75 3.9062 25.7344 Q3.9062 34.125 6.6875 40.4297 Q9.4688 46.7344 15.1562 49.8828 Q20.8438 53.0312 27.5469 53.0312 Q35.9844 53.0312 41.3594 48.7578 Q46.7344 44.4844 48.25 36.625 L39.7031 35.2969 Q38.4844 40.5312 35.3828 43.1641 Q32.2812 45.7969 27.875 45.7969 Q21.2344 45.7969 17.0859 41.0391 Q12.9375 36.2812 12.9375 25.9844 Q12.9375 15.5312 16.9453 10.7969 Q20.9531 6.0625 27.3906 6.0625 Q32.5625 6.0625 36.0312 9.2344 Q39.5 12.4062 40.4375 19 Z"
        /><glyph unicode="m" horiz-adv-x="83.30078" d="M6.5938 0 L6.5938 51.8594 L14.4531 51.8594 L14.4531 44.5781 Q16.8906 48.3906 20.9453 50.7109 Q25 53.0312 30.1719 53.0312 Q35.9375 53.0312 39.625 50.6406 Q43.3125 48.25 44.8281 43.9531 Q50.9844 53.0312 60.8438 53.0312 Q68.5625 53.0312 72.7109 48.7578 Q76.8594 44.4844 76.8594 35.5938 L76.8594 0 L68.1094 0 L68.1094 32.6719 Q68.1094 37.9375 67.2578 40.2578 Q66.4062 42.5781 64.1641 43.9922 Q61.9219 45.4062 58.8906 45.4062 Q53.4219 45.4062 49.8047 41.7734 Q46.1875 38.1406 46.1875 30.125 L46.1875 0 L37.4062 0 L37.4062 33.6875 Q37.4062 39.5469 35.2578 42.4766 Q33.1094 45.4062 28.2188 45.4062 Q24.5156 45.4062 21.3672 43.4531 Q18.2188 41.5 16.7969 37.7422 Q15.375 33.9844 15.375 26.9062 L15.375 0 L6.5938 0 Z"
        /><glyph unicode="s" horiz-adv-x="50.0" d="M3.0781 15.4844 L11.7656 16.8438 Q12.5 11.625 15.8438 8.8438 Q19.1875 6.0625 25.2031 6.0625 Q31.25 6.0625 34.1797 8.5234 Q37.1094 10.9844 37.1094 14.3125 Q37.1094 17.2812 34.5156 19 Q32.7188 20.1719 25.5312 21.9688 Q15.875 24.4219 12.1406 26.2031 Q8.4062 27.9844 6.4766 31.1328 Q4.5469 34.2812 4.5469 38.0938 Q4.5469 41.5469 6.1328 44.5078 Q7.7188 47.4688 10.4531 49.4219 Q12.5 50.9219 16.0391 51.9766 Q19.5781 53.0312 23.6406 53.0312 Q29.7344 53.0312 34.3516 51.2734 Q38.9688 49.5156 41.1641 46.5078 Q43.3594 43.5 44.1875 38.4844 L35.5938 37.3125 Q35.0156 41.3125 32.2031 43.5547 Q29.3906 45.7969 24.2656 45.7969 Q18.2188 45.7969 15.625 43.7969 Q13.0312 41.7969 13.0312 39.1094 Q13.0312 37.4062 14.1094 36.0312 Q15.1875 34.625 17.4844 33.6875 Q18.7969 33.2031 25.25 31.4531 Q34.5781 28.9531 38.2578 27.3672 Q41.9375 25.7812 44.0391 22.7578 Q46.1406 19.7344 46.1406 15.2344 Q46.1406 10.8438 43.5781 6.9609 Q41.0156 3.0781 36.1797 0.9531 Q31.3438 -1.1719 25.25 -1.1719 Q15.1406 -1.1719 9.8438 3.0312 Q4.5469 7.2344 3.0781 15.4844 Z"
        /><glyph unicode="g" horiz-adv-x="55.615234" d="M4.9844 -4.2969 L13.5312 -5.5625 Q14.0625 -9.5156 16.5 -11.3281 Q19.7812 -13.7656 25.4375 -13.7656 Q31.5469 -13.7656 34.8672 -11.3281 Q38.1875 -8.8906 39.3594 -4.5 Q40.0469 -1.8125 39.9844 6.7812 Q34.2344 0 25.6406 0 Q14.9375 0 9.0781 7.7188 Q3.2188 15.4375 3.2188 26.2188 Q3.2188 33.6406 5.9062 39.9141 Q8.5938 46.1875 13.6953 49.6094 Q18.7969 53.0312 25.6875 53.0312 Q34.8594 53.0312 40.8281 45.6094 L40.8281 51.8594 L48.9219 51.8594 L48.9219 7.0312 Q48.9219 -5.0781 46.4609 -10.1328 Q44 -15.1875 38.6484 -18.1172 Q33.2969 -21.0469 25.4844 -21.0469 Q16.2188 -21.0469 10.5 -16.875 Q4.7812 -12.7031 4.9844 -4.2969 ZM12.25 26.8594 Q12.25 16.6562 16.3047 11.9688 Q20.3594 7.2812 26.4688 7.2812 Q32.5156 7.2812 36.6172 11.9453 Q40.7188 16.6094 40.7188 26.5625 Q40.7188 36.0781 36.5 40.9141 Q32.2812 45.75 26.3125 45.75 Q20.4531 45.75 16.3516 40.9922 Q12.25 36.2344 12.25 26.8594 Z"
        /><glyph unicode="y" horiz-adv-x="50.0" d="M6.2031 -19.9688 L5.2188 -11.7188 Q8.1094 -12.5 10.25 -12.5 Q13.1875 -12.5 14.9453 -11.5234 Q16.7031 -10.5469 17.8281 -8.7969 Q18.6562 -7.4688 20.5156 -2.25 Q20.75 -1.5156 21.2969 -0.0938 L1.6094 51.8594 L11.0781 51.8594 L21.875 21.8281 Q23.9688 16.1094 25.6406 9.8125 Q27.1562 15.875 29.25 21.625 L40.3281 51.8594 L49.125 51.8594 L29.3906 -0.875 Q26.2188 -9.4219 24.4688 -12.6406 Q22.125 -17 19.0938 -19.0234 Q16.0625 -21.0469 11.8594 -21.0469 Q9.3281 -21.0469 6.2031 -19.9688 Z"
        /><glyph unicode="v" horiz-adv-x="50.0" d="M21 0 L1.2656 51.8594 L10.5469 51.8594 L21.6875 20.7969 Q23.4844 15.7656 25 10.3594 Q26.1719 14.4531 28.2656 20.2188 L39.7969 51.8594 L48.8281 51.8594 L29.2031 0 L21 0 Z"
        /><glyph unicode="h" horiz-adv-x="55.615234" d="M6.5938 0 L6.5938 71.5781 L15.375 71.5781 L15.375 45.9062 Q21.5312 53.0312 30.9062 53.0312 Q36.6719 53.0312 40.9219 50.7578 Q45.1719 48.4844 47 44.4844 Q48.8281 40.4844 48.8281 32.8594 L48.8281 0 L40.0469 0 L40.0469 32.8594 Q40.0469 39.4531 37.1875 42.4531 Q34.3281 45.4531 29.1094 45.4531 Q25.2031 45.4531 21.7578 43.4297 Q18.3125 41.4062 16.8438 37.9375 Q15.375 34.4688 15.375 28.375 L15.375 0 L6.5938 0 Z"
        /><glyph unicode="w" horiz-adv-x="72.2168" d="M16.1562 0 L0.2969 51.8594 L9.375 51.8594 L17.625 21.9219 L20.7031 10.7969 Q20.9062 11.625 23.3906 21.4844 L31.6406 51.8594 L40.6719 51.8594 L48.4375 21.7812 L51.0312 11.8594 L54 21.875 L62.8906 51.8594 L71.4375 51.8594 L55.2188 0 L46.0938 0 L37.8438 31.0625 L35.8438 39.8906 L25.3438 0 L16.1562 0 Z"
        /><glyph unicode="n" horiz-adv-x="55.615234" d="M6.5938 0 L6.5938 51.8594 L14.5 51.8594 L14.5 44.4844 Q20.2188 53.0312 31 53.0312 Q35.6875 53.0312 39.625 51.3438 Q43.5625 49.6562 45.5156 46.9219 Q47.4688 44.1875 48.25 40.4375 Q48.7344 37.9844 48.7344 31.8906 L48.7344 0 L39.9375 0 L39.9375 31.5469 Q39.9375 36.9219 38.9141 39.5781 Q37.8906 42.2344 35.2812 43.8203 Q32.6719 45.4062 29.1562 45.4062 Q23.5312 45.4062 19.4531 41.8438 Q15.375 38.2812 15.375 28.3281 L15.375 0 L6.5938 0 Z"
        /><glyph unicode="t" horiz-adv-x="27.783203" d="M25.7812 7.8594 L27.0469 0.0938 Q23.3438 -0.6875 20.4062 -0.6875 Q15.625 -0.6875 12.9922 0.8281 Q10.3594 2.3438 9.2812 4.8125 Q8.2031 7.2812 8.2031 15.1875 L8.2031 45.0156 L1.7656 45.0156 L1.7656 51.8594 L8.2031 51.8594 L8.2031 64.7031 L16.9375 69.9688 L16.9375 51.8594 L25.7812 51.8594 L25.7812 45.0156 L16.9375 45.0156 L16.9375 14.7031 Q16.9375 10.9375 17.4062 9.8672 Q17.875 8.7969 18.9219 8.1562 Q19.9688 7.5156 21.9219 7.5156 Q23.3906 7.5156 25.7812 7.8594 Z"
        /><glyph unicode="z" horiz-adv-x="50.0" d="M1.9531 0 L1.9531 7.125 L34.9688 45.0156 Q29.3438 44.7344 25.0469 44.7344 L3.9062 44.7344 L3.9062 51.8594 L46.2969 51.8594 L46.2969 46.0469 L18.2188 13.1406 L12.7969 7.125 Q18.7031 7.5625 23.875 7.5625 L47.8594 7.5625 L47.8594 0 L1.9531 0 Z"
        /><glyph unicode="i" horiz-adv-x="22.216797" d="M6.6406 61.4688 L6.6406 71.5781 L15.4375 71.5781 L15.4375 61.4688 L6.6406 61.4688 ZM6.6406 0 L6.6406 51.8594 L15.4375 51.8594 L15.4375 0 L6.6406 0 Z"
        /><glyph unicode="r" horiz-adv-x="33.30078" d="M6.5 0 L6.5 51.8594 L14.4062 51.8594 L14.4062 44 Q17.4375 49.5156 20 51.2734 Q22.5625 53.0312 25.6406 53.0312 Q30.0781 53.0312 34.6719 50.2031 L31.6406 42.0469 Q28.4219 43.9531 25.2031 43.9531 Q22.3125 43.9531 20.0156 42.2188 Q17.7188 40.4844 16.75 37.4062 Q15.2812 32.7188 15.2812 27.1562 L15.2812 0 L6.5 0 Z"
        /><glyph unicode="a" horiz-adv-x="55.615234" d="M40.4375 6.3906 Q35.5469 2.25 31.0312 0.5391 Q26.5156 -1.1719 21.3438 -1.1719 Q12.7969 -1.1719 8.2031 3 Q3.6094 7.1719 3.6094 13.6719 Q3.6094 17.4844 5.3438 20.6328 Q7.0781 23.7812 9.8906 25.6875 Q12.7031 27.5938 16.2188 28.5625 Q18.7969 29.25 24.0312 29.8906 Q34.6719 31.1562 39.7031 32.9062 Q39.75 34.7188 39.75 35.2031 Q39.75 40.5781 37.25 42.7812 Q33.8906 45.75 27.25 45.75 Q21.0469 45.75 18.0938 43.5781 Q15.1406 41.4062 13.7188 35.8906 L5.125 37.0625 Q6.2969 42.5781 8.9844 45.9688 Q11.6719 49.3594 16.75 51.1953 Q21.8281 53.0312 28.5156 53.0312 Q35.1562 53.0312 39.3047 51.4688 Q43.4531 49.9062 45.4062 47.5391 Q47.3594 45.1719 48.1406 41.5469 Q48.5781 39.3125 48.5781 33.4531 L48.5781 21.7344 Q48.5781 9.4688 49.1406 6.2266 Q49.7031 2.9844 51.375 0 L42.1875 0 Q40.8281 2.7344 40.4375 6.3906 ZM39.7031 26.0312 Q34.9062 24.0781 25.3438 22.7031 Q19.9219 21.9219 17.6797 20.9453 Q15.4375 19.9688 14.2109 18.0938 Q12.9844 16.2188 12.9844 13.9219 Q12.9844 10.4062 15.6484 8.0625 Q18.3125 5.7188 23.4375 5.7188 Q28.5156 5.7188 32.4688 7.9375 Q36.4219 10.1562 38.2812 14.0156 Q39.7031 17 39.7031 22.7969 L39.7031 26.0312 Z"
        /><glyph unicode="l" horiz-adv-x="22.216797" d="M6.3906 0 L6.3906 71.5781 L15.1875 71.5781 L15.1875 0 L6.3906 0 Z"
        /><glyph unicode="o" horiz-adv-x="55.615234" d="M3.3281 25.9219 Q3.3281 40.3281 11.3281 47.2656 Q18.0156 53.0312 27.6406 53.0312 Q38.3281 53.0312 45.1172 46.0234 Q51.9062 39.0156 51.9062 26.6562 Q51.9062 16.6562 48.9062 10.9141 Q45.9062 5.1719 40.1641 2 Q34.4219 -1.1719 27.6406 -1.1719 Q16.75 -1.1719 10.0391 5.8125 Q3.3281 12.7969 3.3281 25.9219 ZM12.3594 25.9219 Q12.3594 15.9688 16.7031 11.0156 Q21.0469 6.0625 27.6406 6.0625 Q34.1875 6.0625 38.5312 11.0391 Q42.875 16.0156 42.875 26.2188 Q42.875 35.8438 38.5 40.7969 Q34.125 45.75 27.6406 45.75 Q21.0469 45.75 16.7031 40.8203 Q12.3594 35.8906 12.3594 25.9219 Z"
        /><glyph unicode="p" horiz-adv-x="55.615234" d="M6.5938 -19.875 L6.5938 51.8594 L14.5938 51.8594 L14.5938 45.125 Q17.4375 49.0781 21 51.0547 Q24.5625 53.0312 29.6406 53.0312 Q36.2812 53.0312 41.3594 49.6094 Q46.4375 46.1875 49.0234 39.9609 Q51.6094 33.7344 51.6094 26.3125 Q51.6094 18.3594 48.7578 11.9844 Q45.9062 5.6094 40.4609 2.2188 Q35.0156 -1.1719 29 -1.1719 Q24.6094 -1.1719 21.1172 0.6875 Q17.625 2.5469 15.375 5.375 L15.375 -19.875 L6.5938 -19.875 ZM14.5469 25.6406 Q14.5469 15.625 18.6016 10.8438 Q22.6562 6.0625 28.4219 6.0625 Q34.2812 6.0625 38.4531 11.0156 Q42.625 15.9688 42.625 26.375 Q42.625 36.2812 38.5469 41.2109 Q34.4688 46.1406 28.8125 46.1406 Q23.1875 46.1406 18.8672 40.8906 Q14.5469 35.6406 14.5469 25.6406 Z"
        /><glyph unicode=" " horiz-adv-x="27.783203" d=""
        /><glyph unicode="X" horiz-adv-x="66.69922" d="M0.4375 0 L28.125 37.3125 L3.7188 71.5781 L14.9844 71.5781 L27.9844 53.2188 Q32.0312 47.5156 33.7344 44.4375 Q36.1406 48.3438 39.4062 52.5938 L53.8125 71.5781 L64.1094 71.5781 L38.9688 37.8438 L66.0625 0 L54.3438 0 L36.3281 25.5312 Q34.8125 27.7344 33.2031 30.3281 Q30.8125 26.4219 29.7812 24.9531 L11.8125 0 L0.4375 0 Z"
        /><glyph unicode="Y" horiz-adv-x="66.69922" d="M27.875 0 L27.875 30.3281 L0.2969 71.5781 L11.8125 71.5781 L25.9219 50 Q29.8281 43.9531 33.2031 37.8906 Q36.4219 43.5 41.0156 50.5312 L54.8906 71.5781 L65.9219 71.5781 L37.3594 30.3281 L37.3594 0 L27.875 0 Z"
        /><glyph unicode="e" horiz-adv-x="55.615234" d="M42.0938 16.7031 L51.1719 15.5781 Q49.0312 7.625 43.2188 3.2266 Q37.4062 -1.1719 28.375 -1.1719 Q17 -1.1719 10.3281 5.8359 Q3.6562 12.8438 3.6562 25.4844 Q3.6562 38.5781 10.3984 45.8047 Q17.1406 53.0312 27.875 53.0312 Q38.2812 53.0312 44.875 45.9531 Q51.4688 38.875 51.4688 26.0312 Q51.4688 25.25 51.4219 23.6875 L12.75 23.6875 Q13.2344 15.1406 17.5781 10.6016 Q21.9219 6.0625 28.4219 6.0625 Q33.25 6.0625 36.6719 8.6016 Q40.0938 11.1406 42.0938 16.7031 ZM13.2344 30.9062 L42.1875 30.9062 Q41.6094 37.4531 38.875 40.7188 Q34.6719 45.7969 27.9844 45.7969 Q21.9219 45.7969 17.7969 41.75 Q13.6719 37.7031 13.2344 30.9062 Z"
        /><glyph unicode="f" horiz-adv-x="27.783203" d="M8.6875 0 L8.6875 45.0156 L0.9219 45.0156 L0.9219 51.8594 L8.6875 51.8594 L8.6875 57.375 Q8.6875 62.5938 9.625 65.1406 Q10.8906 68.5625 14.0859 70.6797 Q17.2812 72.7969 23.0469 72.7969 Q26.7656 72.7969 31.25 71.9219 L29.9375 64.2656 Q27.2031 64.75 24.75 64.75 Q20.75 64.75 19.0938 63.0391 Q17.4375 61.3281 17.4375 56.6406 L17.4375 51.8594 L27.5469 51.8594 L27.5469 45.0156 L17.4375 45.0156 L17.4375 0 L8.6875 0 Z"
        /><glyph unicode="D" horiz-adv-x="72.2168" d="M7.7188 0 L7.7188 71.5781 L32.375 71.5781 Q40.7188 71.5781 45.125 70.5625 Q51.2656 69.1406 55.6094 65.4375 Q61.2812 60.6406 64.0859 53.1953 Q66.8906 45.75 66.8906 36.1875 Q66.8906 28.0312 64.9922 21.7344 Q63.0938 15.4375 60.1094 11.3047 Q57.125 7.1719 53.5859 4.8047 Q50.0469 2.4375 45.0469 1.2188 Q40.0469 0 33.5469 0 L7.7188 0 ZM17.1875 8.4531 L32.4688 8.4531 Q39.5469 8.4531 43.5781 9.7656 Q47.6094 11.0781 50 13.4844 Q53.375 16.8438 55.25 22.5312 Q57.125 28.2188 57.125 36.3281 Q57.125 47.5625 53.4375 53.5938 Q49.75 59.625 44.4844 61.6719 Q40.6719 63.1406 32.2344 63.1406 L17.1875 63.1406 L17.1875 8.4531 Z"
      /></font
      ><font horiz-adv-x="77.7832" id="font2"
      ><font-face ascent="91.23535" descent="19.506836" units-per-em="100" style="font-style:normal; font-family:Times New Roman; font-weight:normal;"
        /><missing-glyph horiz-adv-x="77.7832" d="M13.875 0 L13.875 62.5 L63.875 62.5 L63.875 0 L13.875 0 ZM15.4375 1.5625 L62.3125 1.5625 L62.3125 60.9375 L15.4375 60.9375 L15.4375 1.5625 Z"
        /><glyph unicode="t" horiz-adv-x="27.783203" d="M16.1094 59.4219 L16.1094 44.7344 L26.5625 44.7344 L26.5625 41.3125 L16.1094 41.3125 L16.1094 12.3125 Q16.1094 7.9531 17.3594 6.4453 Q18.6094 4.9375 20.5625 4.9375 Q22.1719 4.9375 23.6875 5.9375 Q25.2031 6.9375 26.0312 8.8906 L27.9375 8.8906 Q26.2188 4.1094 23.0938 1.6875 Q19.9688 -0.7344 16.6562 -0.7344 Q14.4062 -0.7344 12.2578 0.5156 Q10.1094 1.7656 9.0859 4.0781 Q8.0625 6.3906 8.0625 11.2344 L8.0625 41.3125 L0.9844 41.3125 L0.9844 42.9219 Q3.6562 44 6.4688 46.5625 Q9.2812 49.125 11.4688 52.6406 Q12.5938 54.5 14.5938 59.4219 L16.1094 59.4219 Z"
        /><glyph unicode="g" horiz-adv-x="50.0" d="M15.0938 16.3125 Q10.9844 18.3125 8.7891 21.8984 Q6.5938 25.4844 6.5938 29.8281 Q6.5938 36.4688 11.6016 41.2578 Q16.6094 46.0469 24.4219 46.0469 Q30.8125 46.0469 35.5 42.9219 L44.9688 42.9219 Q47.0781 42.9219 47.4141 42.7969 Q47.75 42.6719 47.9062 42.3906 Q48.1875 41.9375 48.1875 40.8281 Q48.1875 39.5469 47.9531 39.0625 Q47.7969 38.8125 47.4375 38.6719 Q47.0781 38.5312 44.9688 38.5312 L39.1562 38.5312 Q41.8906 35.0156 41.8906 29.5469 Q41.8906 23.2969 37.1094 18.8516 Q32.3281 14.4062 24.2656 14.4062 Q20.9531 14.4062 17.4844 15.375 Q15.3281 13.5312 14.5703 12.1406 Q13.8125 10.75 13.8125 9.7656 Q13.8125 8.9375 14.625 8.1562 Q15.4375 7.375 17.7812 7.0312 Q19.1406 6.8438 24.6094 6.6875 Q34.6719 6.4531 37.6406 6 Q42.1875 5.375 44.8984 2.6406 Q47.6094 -0.0938 47.6094 -4.1094 Q47.6094 -9.625 42.4375 -14.4531 Q34.8125 -21.5781 22.5625 -21.5781 Q13.1406 -21.5781 6.6406 -17.3281 Q2.9844 -14.8906 2.9844 -12.25 Q2.9844 -11.0781 3.5156 -9.9062 Q4.3438 -8.1094 6.9375 -4.8906 Q7.2812 -4.4375 11.9219 0.3906 Q9.375 1.9062 8.3281 3.1016 Q7.2812 4.2969 7.2812 5.8125 Q7.2812 7.5156 8.6719 9.8125 Q10.0625 12.1094 15.0938 16.3125 ZM23.5781 43.7031 Q19.9688 43.7031 17.5312 40.8203 Q15.0938 37.9375 15.0938 31.9844 Q15.0938 24.2656 18.4062 20.0156 Q20.9531 16.7969 24.8594 16.7969 Q28.5625 16.7969 30.9531 19.5781 Q33.3438 22.3594 33.3438 28.3281 Q33.3438 36.0781 29.9844 40.4844 Q27.4844 43.7031 23.5781 43.7031 ZM14.5938 0 Q12.3125 -2.4844 11.1406 -4.6328 Q9.9688 -6.7812 9.9688 -8.5938 Q9.9688 -10.9375 12.7969 -12.7031 Q17.6719 -15.7188 26.9062 -15.7188 Q35.6875 -15.7188 39.8672 -12.6172 Q44.0469 -9.5156 44.0469 -6 Q44.0469 -3.4688 41.5469 -2.3906 Q39.0156 -1.3125 31.5 -1.125 Q20.5156 -0.8281 14.5938 0 Z"
        /><glyph unicode="n" horiz-adv-x="50.0" d="M16.1562 36.5781 Q24.0312 46.0469 31.1562 46.0469 Q34.8125 46.0469 37.4531 44.2188 Q40.0938 42.3906 41.6562 38.1875 Q42.7188 35.25 42.7188 29.2031 L42.7188 10.1094 Q42.7188 5.8594 43.4062 4.3438 Q43.9531 3.125 45.1484 2.4453 Q46.3438 1.7656 49.5625 1.7656 L49.5625 0 L27.4375 0 L27.4375 1.7656 L28.375 1.7656 Q31.5 1.7656 32.7422 2.7109 Q33.9844 3.6562 34.4688 5.5156 Q34.6719 6.25 34.6719 10.1094 L34.6719 28.4219 Q34.6719 34.5156 33.0859 37.2812 Q31.5 40.0469 27.7344 40.0469 Q21.9219 40.0469 16.1562 33.6875 L16.1562 10.1094 Q16.1562 5.5625 16.7031 4.5 Q17.3906 3.0781 18.5859 2.4219 Q19.7812 1.7656 23.4375 1.7656 L23.4375 0 L1.3125 0 L1.3125 1.7656 L2.2969 1.7656 Q5.7188 1.7656 6.9141 3.4922 Q8.1094 5.2188 8.1094 10.1094 L8.1094 26.7031 Q8.1094 34.7656 7.7422 36.5234 Q7.375 38.2812 6.6172 38.9141 Q5.8594 39.5469 4.5938 39.5469 Q3.2188 39.5469 1.3125 38.8125 L0.5938 40.5781 L14.0625 46.0469 L16.1562 46.0469 L16.1562 36.5781 Z"
        /><glyph unicode="i" horiz-adv-x="27.783203" d="M14.5 69.4375 Q16.5469 69.4375 17.9922 67.9922 Q19.4375 66.5469 19.4375 64.5 Q19.4375 62.4531 17.9922 60.9844 Q16.5469 59.5156 14.5 59.5156 Q12.4531 59.5156 10.9844 60.9844 Q9.5156 62.4531 9.5156 64.5 Q9.5156 66.5469 10.9609 67.9922 Q12.4062 69.4375 14.5 69.4375 ZM18.5625 46.0469 L18.5625 10.1094 Q18.5625 5.9062 19.1719 4.5156 Q19.7812 3.125 20.9766 2.4453 Q22.1719 1.7656 25.3438 1.7656 L25.3438 0 L3.6094 0 L3.6094 1.7656 Q6.8906 1.7656 8.0078 2.3984 Q9.125 3.0312 9.7891 4.4922 Q10.4531 5.9531 10.4531 10.1094 L10.4531 27.3438 Q10.4531 34.625 10.0156 36.7656 Q9.6719 38.3281 8.9375 38.9375 Q8.2031 39.5469 6.9375 39.5469 Q5.5625 39.5469 3.6094 38.8125 L2.9375 40.5781 L16.4062 46.0469 L18.5625 46.0469 Z"
        /><glyph unicode="l" horiz-adv-x="27.783203" d="M18.5 69.4375 L18.5 10.1094 Q18.5 5.9062 19.1172 4.5391 Q19.7344 3.1719 21 2.4688 Q22.2656 1.7656 25.7344 1.7656 L25.7344 0 L3.8125 0 L3.8125 1.7656 Q6.8906 1.7656 8.0078 2.3984 Q9.125 3.0312 9.7656 4.4922 Q10.4062 5.9531 10.4062 10.1094 L10.4062 50.7344 Q10.4062 58.2969 10.0625 60.0312 Q9.7188 61.7656 8.9609 62.3984 Q8.2031 63.0312 7.0312 63.0312 Q5.7656 63.0312 3.8125 62.25 L2.9844 63.9688 L16.3125 69.4375 L18.5 69.4375 Z"
        /><glyph unicode="p" horiz-adv-x="50.0" d="M-0.0938 40.2812 L13.6719 45.8438 L15.5312 45.8438 L15.5312 35.4062 Q19 41.3125 22.4922 43.6797 Q25.9844 46.0469 29.8281 46.0469 Q36.5781 46.0469 41.0625 40.7656 Q46.5781 34.3281 46.5781 23.9688 Q46.5781 12.4062 39.9375 4.8281 Q34.4688 -1.375 26.1719 -1.375 Q22.5625 -1.375 19.9219 -0.3438 Q17.9688 0.3906 15.5312 2.5938 L15.5312 -11.0312 Q15.5312 -15.625 16.0938 -16.8672 Q16.6562 -18.1094 18.0469 -18.8438 Q19.4375 -19.5781 23.0938 -19.5781 L23.0938 -21.3906 L-0.3438 -21.3906 L-0.3438 -19.5781 L0.875 -19.5781 Q3.5625 -19.625 5.4688 -18.5625 Q6.3906 -18.0156 6.9062 -16.8203 Q7.4219 -15.625 7.4219 -10.75 L7.4219 31.5469 Q7.4219 35.8906 7.0312 37.0625 Q6.6406 38.2344 5.7891 38.8203 Q4.9375 39.4062 3.4688 39.4062 Q2.2969 39.4062 0.4844 38.7188 L-0.0938 40.2812 ZM15.5312 32.5156 L15.5312 15.8281 Q15.5312 10.4062 15.9688 8.6875 Q16.6562 5.8594 19.3125 3.7109 Q21.9688 1.5625 26.0312 1.5625 Q30.9062 1.5625 33.9375 5.375 Q37.8906 10.3594 37.8906 19.3906 Q37.8906 29.6406 33.4062 35.1562 Q30.2812 38.9688 25.9844 38.9688 Q23.6406 38.9688 21.3438 37.7969 Q19.5781 36.9219 15.5312 32.5156 Z"
        /><glyph unicode="a" horiz-adv-x="44.384766" d="M28.4688 6.4531 Q21.5781 1.125 19.8281 0.2969 Q17.1875 -0.9219 14.2031 -0.9219 Q9.5781 -0.9219 6.5703 2.25 Q3.5625 5.4219 3.5625 10.5938 Q3.5625 13.875 5.0312 16.2656 Q7.0312 19.5781 11.9844 22.5078 Q16.9375 25.4375 28.4688 29.6406 L28.4688 31.3906 Q28.4688 38.0938 26.3438 40.5781 Q24.2188 43.0625 20.1719 43.0625 Q17.0938 43.0625 15.2812 41.4062 Q13.4219 39.75 13.4219 37.5938 L13.5312 34.7656 Q13.5312 32.5156 12.3828 31.2969 Q11.2344 30.0781 9.375 30.0781 Q7.5625 30.0781 6.4219 31.3516 Q5.2812 32.625 5.2812 34.8125 Q5.2812 39.0156 9.5781 42.5312 Q13.875 46.0469 21.625 46.0469 Q27.5938 46.0469 31.3906 44.0469 Q34.2812 42.5312 35.6406 39.3125 Q36.5312 37.2031 36.5312 30.7188 L36.5312 15.5312 Q36.5312 9.125 36.7734 7.6875 Q37.0156 6.25 37.5781 5.7656 Q38.1406 5.2812 38.875 5.2812 Q39.6562 5.2812 40.2344 5.6094 Q41.2656 6.25 44.1875 9.1875 L44.1875 6.4531 Q38.7188 -0.875 33.7344 -0.875 Q31.3438 -0.875 29.9297 0.7812 Q28.5156 2.4375 28.4688 6.4531 ZM28.4688 9.625 L28.4688 26.6562 Q21.0938 23.7344 18.9531 22.5156 Q15.0938 20.3594 13.4297 18.0156 Q11.7656 15.6719 11.7656 12.8906 Q11.7656 9.375 13.8672 7.0547 Q15.9688 4.7344 18.7031 4.7344 Q22.4062 4.7344 28.4688 9.625 Z"
        /><glyph unicode="s" horiz-adv-x="38.916016" d="M32.0312 46.0469 L32.0312 30.8125 L30.4219 30.8125 Q28.5625 37.9844 25.6562 40.5781 Q22.75 43.1719 18.2656 43.1719 Q14.8438 43.1719 12.7422 41.3594 Q10.6406 39.5469 10.6406 37.3594 Q10.6406 34.625 12.2031 32.6719 Q13.7188 30.6719 18.3594 28.4219 L25.4844 24.9531 Q35.4062 20.125 35.4062 12.2031 Q35.4062 6.1094 30.7891 2.3672 Q26.1719 -1.375 20.4531 -1.375 Q16.3594 -1.375 11.0781 0.0938 Q9.4688 0.5938 8.4531 0.5938 Q7.3281 0.5938 6.6875 -0.6875 L5.0781 -0.6875 L5.0781 15.2812 L6.6875 15.2812 Q8.0625 8.4531 11.9141 4.9844 Q15.7656 1.5156 20.5625 1.5156 Q23.9219 1.5156 26.0469 3.4922 Q28.1719 5.4688 28.1719 8.25 Q28.1719 11.625 25.8047 13.9219 Q23.4375 16.2188 16.3594 19.7344 Q9.2812 23.25 7.0781 26.0781 Q4.8906 28.8594 4.8906 33.1094 Q4.8906 38.625 8.6719 42.3359 Q12.4531 46.0469 18.4531 46.0469 Q21.0938 46.0469 24.8594 44.9219 Q27.3438 44.1875 28.1719 44.1875 Q28.9531 44.1875 29.3906 44.5312 Q29.8281 44.875 30.4219 46.0469 L32.0312 46.0469 Z"
        /><glyph unicode="f" horiz-adv-x="33.30078" d="M20.6094 41.2188 L20.6094 11.8125 Q20.6094 5.5625 21.9688 3.9062 Q23.7812 1.7656 26.8125 1.7656 L30.8594 1.7656 L30.8594 0 L4.1562 0 L4.1562 1.7656 L6.1562 1.7656 Q8.1094 1.7656 9.7188 2.7422 Q11.3281 3.7188 11.9375 5.375 Q12.5469 7.0312 12.5469 11.8125 L12.5469 41.2188 L3.8594 41.2188 L3.8594 44.7344 L12.5469 44.7344 L12.5469 47.6562 Q12.5469 54.3438 14.6953 58.9844 Q16.8438 63.625 21.2656 66.4844 Q25.6875 69.3438 31.2031 69.3438 Q36.3281 69.3438 40.625 66.0156 Q43.4531 63.8125 43.4531 61.0781 Q43.4531 59.625 42.1875 58.3281 Q40.9219 57.0312 39.4531 57.0312 Q38.3281 57.0312 37.0859 57.8359 Q35.8438 58.6406 34.0391 61.3047 Q32.2344 63.9688 30.7188 64.8906 Q29.2031 65.8281 27.3438 65.8281 Q25.0938 65.8281 23.5312 64.625 Q21.9688 63.4219 21.2891 60.9141 Q20.6094 58.4062 20.6094 47.9531 L20.6094 44.7344 L32.125 44.7344 L32.125 41.2188 L20.6094 41.2188 Z"
        /><glyph unicode="o" horiz-adv-x="50.0" d="M25 46.0469 Q35.1562 46.0469 41.3125 38.3281 Q46.5312 31.7344 46.5312 23.1875 Q46.5312 17.1875 43.6484 11.0391 Q40.7656 4.8906 35.7188 1.7578 Q30.6719 -1.375 24.4688 -1.375 Q14.3594 -1.375 8.4062 6.6875 Q3.375 13.4844 3.375 21.9219 Q3.375 28.0781 6.4219 34.1562 Q9.4688 40.2344 14.4531 43.1406 Q19.4375 46.0469 25 46.0469 ZM23.4844 42.875 Q20.9062 42.875 18.2891 41.3359 Q15.6719 39.7969 14.0625 35.9375 Q12.4531 32.0781 12.4531 26.0312 Q12.4531 16.2656 16.3359 9.1797 Q20.2188 2.0938 26.5625 2.0938 Q31.2969 2.0938 34.375 6 Q37.4531 9.9062 37.4531 19.4375 Q37.4531 31.3438 32.3281 38.1875 Q28.8594 42.875 23.4844 42.875 Z"
        /><glyph unicode=" " horiz-adv-x="25.0" d=""
        /><glyph unicode="r" horiz-adv-x="33.30078" d="M16.2188 46.0469 L16.2188 35.9844 Q21.8281 46.0469 27.7344 46.0469 Q30.4219 46.0469 32.1797 44.4141 Q33.9375 42.7812 33.9375 40.625 Q33.9375 38.7188 32.6641 37.3984 Q31.3906 36.0781 29.6406 36.0781 Q27.9375 36.0781 25.8125 37.7656 Q23.6875 39.4531 22.6562 39.4531 Q21.7812 39.4531 20.75 38.4844 Q18.5625 36.4688 16.2188 31.8906 L16.2188 10.4531 Q16.2188 6.7344 17.1406 4.8281 Q17.7812 3.5156 19.3906 2.6406 Q21 1.7656 24.0312 1.7656 L24.0312 0 L1.125 0 L1.125 1.7656 Q4.5469 1.7656 6.2031 2.8281 Q7.4219 3.6094 7.9062 5.3281 Q8.1562 6.1562 8.1562 10.0625 L8.1562 27.3906 Q8.1562 35.2031 7.8359 36.6953 Q7.5156 38.1875 6.6641 38.8672 Q5.8125 39.5469 4.5469 39.5469 Q3.0312 39.5469 1.125 38.8125 L0.6406 40.5781 L14.1562 46.0469 L16.2188 46.0469 Z"
        /><glyph unicode="e" horiz-adv-x="44.384766" d="M10.6406 27.875 Q10.5938 17.9219 15.4844 12.25 Q20.3594 6.5938 26.9531 6.5938 Q31.3438 6.5938 34.5938 9.0078 Q37.8438 11.4219 40.0469 17.2812 L41.5469 16.3125 Q40.5312 9.625 35.6016 4.125 Q30.6719 -1.375 23.25 -1.375 Q15.1875 -1.375 9.4531 4.9062 Q3.7188 11.1875 3.7188 21.7812 Q3.7188 33.25 9.6016 39.6719 Q15.4844 46.0938 24.3594 46.0938 Q31.8906 46.0938 36.7188 41.1406 Q41.5469 36.1875 41.5469 27.875 L10.6406 27.875 ZM10.6406 30.7188 L31.3438 30.7188 Q31.1094 35.0156 30.3281 36.7656 Q29.1094 39.5 26.6875 41.0625 Q24.2656 42.625 21.625 42.625 Q17.5781 42.625 14.3828 39.4766 Q11.1875 36.3281 10.6406 30.7188 Z"
        /><glyph unicode="b" horiz-adv-x="50.0" d="M15.375 37.0156 Q21.875 46.0469 29.3906 46.0469 Q36.2812 46.0469 41.4062 40.1641 Q46.5312 34.2812 46.5312 24.0781 Q46.5312 12.1562 38.625 4.8906 Q31.8438 -1.375 23.4844 -1.375 Q19.5781 -1.375 15.5547 0.0469 Q11.5312 1.4688 7.3281 4.2969 L7.3281 50.6406 Q7.3281 58.25 6.9609 60.0078 Q6.5938 61.7656 5.8125 62.3984 Q5.0312 63.0312 3.8594 63.0312 Q2.4844 63.0312 0.4375 62.25 L-0.25 63.9688 L13.1875 69.4375 L15.375 69.4375 L15.375 37.0156 ZM15.375 33.8906 L15.375 7.125 Q17.875 4.6875 20.5312 3.4453 Q23.1875 2.2031 25.9844 2.2031 Q30.4219 2.2031 34.2578 7.0859 Q38.0938 11.9688 38.0938 21.2969 Q38.0938 29.8906 34.2578 34.5 Q30.4219 39.1094 25.5312 39.1094 Q22.9531 39.1094 20.3594 37.7969 Q18.4062 36.8125 15.375 33.8906 Z"
        /><glyph unicode="m" horiz-adv-x="77.7832" d="M16.4062 36.5312 Q21.2969 41.4062 22.1719 42.1406 Q24.3594 44 26.8984 45.0234 Q29.4375 46.0469 31.9375 46.0469 Q36.1406 46.0469 39.1641 43.6016 Q42.1875 41.1562 43.2188 36.5312 Q48.25 42.3906 51.7109 44.2188 Q55.1719 46.0469 58.8438 46.0469 Q62.4062 46.0469 65.1641 44.2188 Q67.9219 42.3906 69.5312 38.2344 Q70.6094 35.4062 70.6094 29.3438 L70.6094 10.1094 Q70.6094 5.9062 71.2344 4.3438 Q71.7344 3.2656 73.0469 2.5156 Q74.3594 1.7656 77.3438 1.7656 L77.3438 0 L55.2812 0 L55.2812 1.7656 L56.2031 1.7656 Q59.0781 1.7656 60.6875 2.875 Q61.8125 3.6562 62.3125 5.375 Q62.5 6.2031 62.5 10.1094 L62.5 29.3438 Q62.5 34.8125 61.1875 37.0625 Q59.2812 40.1875 55.0781 40.1875 Q52.4844 40.1875 49.875 38.8906 Q47.2656 37.5938 43.5625 34.0781 L43.4531 33.5469 L43.5625 31.4531 L43.5625 10.1094 Q43.5625 5.5156 44.0703 4.3906 Q44.5781 3.2656 45.9922 2.5156 Q47.4062 1.7656 50.8281 1.7656 L50.8281 0 L28.2188 0 L28.2188 1.7656 Q31.9375 1.7656 33.3281 2.6406 Q34.7188 3.5156 35.25 5.2812 Q35.5 6.1094 35.5 10.1094 L35.5 29.3438 Q35.5 34.8125 33.8906 37.2031 Q31.7344 40.3281 27.875 40.3281 Q25.25 40.3281 22.6562 38.9219 Q18.6094 36.7656 16.4062 34.0781 L16.4062 10.1094 Q16.4062 5.7188 17.0156 4.3984 Q17.625 3.0781 18.8203 2.4219 Q20.0156 1.7656 23.6875 1.7656 L23.6875 0 L1.5625 0 L1.5625 1.7656 Q4.6406 1.7656 5.8594 2.4219 Q7.0781 3.0781 7.7109 4.5156 Q8.3438 5.9531 8.3438 10.1094 L8.3438 27.2031 Q8.3438 34.5781 7.9062 36.7188 Q7.5625 38.3281 6.8359 38.9375 Q6.1094 39.5469 4.8281 39.5469 Q3.4688 39.5469 1.5625 38.8125 L0.8281 40.5781 L14.3125 46.0469 L16.4062 46.0469 L16.4062 36.5312 Z"
        /><glyph unicode="u" horiz-adv-x="50.0" d="M42.3281 44.7344 L42.3281 17.625 Q42.3281 9.8594 42.6953 8.125 Q43.0625 6.3906 43.8672 5.7109 Q44.6719 5.0312 45.75 5.0312 Q47.2656 5.0312 49.1719 5.8594 L49.8594 4.1562 L36.4688 -1.375 L34.2812 -1.375 L34.2812 8.1094 Q28.5156 1.8594 25.4922 0.2422 Q22.4688 -1.375 19.0938 -1.375 Q15.3281 -1.375 12.5703 0.8047 Q9.8125 2.9844 8.7422 6.3984 Q7.6719 9.8125 7.6719 16.0625 L7.6719 36.0312 Q7.6719 39.2031 6.9844 40.4297 Q6.2969 41.6562 4.9531 42.3125 Q3.6094 42.9688 0.0938 42.9219 L0.0938 44.7344 L15.7656 44.7344 L15.7656 14.7969 Q15.7656 8.5469 17.9453 6.5938 Q20.125 4.6406 23.1875 4.6406 Q25.2969 4.6406 27.9531 5.9609 Q30.6094 7.2812 34.2812 10.9844 L34.2812 36.3281 Q34.2812 40.1406 32.8906 41.4844 Q31.5 42.8281 27.0938 42.9219 L27.0938 44.7344 L42.3281 44.7344 Z"
        /><glyph unicode="N" horiz-adv-x="72.2168" d="M-1.3125 66.2188 L16.6562 66.2188 L57.125 16.5469 L57.125 54.7344 Q57.125 60.8438 55.7656 62.3594 Q53.9531 64.4062 50.0469 64.4062 L47.75 64.4062 L47.75 66.2188 L70.7969 66.2188 L70.7969 64.4062 L68.4531 64.4062 Q64.2656 64.4062 62.5 61.8594 Q61.4219 60.2969 61.4219 54.7344 L61.4219 -1.0781 L59.6719 -1.0781 L16.0156 52.25 L16.0156 11.4688 Q16.0156 5.375 17.3281 3.8594 Q19.1875 1.8125 23.0469 1.8125 L25.3906 1.8125 L25.3906 0 L2.3438 0 L2.3438 1.8125 L4.6406 1.8125 Q8.8906 1.8125 10.6406 4.3438 Q11.7188 5.9062 11.7188 11.4688 L11.7188 57.5156 Q8.8438 60.8906 7.3516 61.9609 Q5.8594 63.0312 2.9844 63.9688 Q1.5625 64.4062 -1.3125 64.4062 L-1.3125 66.2188 Z"
        /><glyph unicode=")" horiz-adv-x="33.30078" d="M2.25 67.3906 L2.25 69.4375 Q9.6719 65.7656 14.5938 60.7969 Q21.5781 53.6562 25.3906 44.0625 Q29.2031 34.4688 29.2031 24.0781 Q29.2031 8.9375 21.7578 -3.5391 Q14.3125 -16.0156 2.25 -21.3906 L2.25 -19.5781 Q8.25 -16.2188 12.1328 -10.4766 Q16.0156 -4.7344 17.8984 4.125 Q19.7812 12.9844 19.7812 22.6094 Q19.7812 33.0156 18.1719 41.6094 Q16.9375 48.3438 15.1094 52.3984 Q13.2812 56.4531 10.2578 60.2109 Q7.2344 63.9688 2.25 67.3906 Z"
        /><glyph unicode="B" horiz-adv-x="66.69922" d="M46.1875 33.7969 Q53.0781 32.3281 56.5 29.1094 Q61.2344 24.6094 61.2344 18.1094 Q61.2344 13.1875 58.1094 8.6719 Q54.9844 4.1562 49.5391 2.0781 Q44.0938 0 32.9062 0 L1.6562 0 L1.6562 1.8125 L4.1562 1.8125 Q8.2969 1.8125 10.1094 4.4375 Q11.2344 6.1562 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.6406 9.8125 62.25 Q7.9062 64.4062 4.1562 64.4062 L1.6562 64.4062 L1.6562 66.2188 L30.2812 66.2188 Q38.2812 66.2188 43.1094 65.0469 Q50.4375 63.2812 54.2969 58.8125 Q58.1562 54.3438 58.1562 48.5312 Q58.1562 43.5625 55.125 39.625 Q52.0938 35.6875 46.1875 33.7969 ZM20.6094 36.4219 Q22.4062 36.0781 24.7266 35.9141 Q27.0469 35.75 29.8281 35.75 Q36.9688 35.75 40.5547 37.2812 Q44.1406 38.8125 46.0469 41.9922 Q47.9531 45.1719 47.9531 48.9219 Q47.9531 54.7344 43.2188 58.8359 Q38.4844 62.9375 29.3906 62.9375 Q24.5156 62.9375 20.6094 61.8594 L20.6094 36.4219 ZM20.6094 4.7812 Q26.2656 3.4688 31.7812 3.4688 Q40.625 3.4688 45.2656 7.4453 Q49.9062 11.4219 49.9062 17.2812 Q49.9062 21.1406 47.8047 24.7031 Q45.7031 28.2656 40.9688 30.3203 Q36.2344 32.375 29.25 32.375 Q26.2188 32.375 24.0703 32.2734 Q21.9219 32.1719 20.6094 31.9375 L20.6094 4.7812 Z"
        /><glyph unicode="d" horiz-adv-x="50.0" d="M34.7188 5.0312 Q31.4531 1.6094 28.3281 0.1172 Q25.2031 -1.375 21.5781 -1.375 Q14.2656 -1.375 8.7969 4.7578 Q3.3281 10.8906 3.3281 20.5156 Q3.3281 30.125 9.3828 38.1094 Q15.4375 46.0938 24.9531 46.0938 Q30.8594 46.0938 34.7188 42.3281 L34.7188 50.5938 Q34.7188 58.25 34.3516 60.0078 Q33.9844 61.7656 33.2031 62.3984 Q32.4219 63.0312 31.25 63.0312 Q29.9844 63.0312 27.875 62.25 L27.25 63.9688 L40.5781 69.4375 L42.7812 69.4375 L42.7812 17.7188 Q42.7812 9.8594 43.1406 8.125 Q43.5 6.3906 44.3125 5.7109 Q45.125 5.0312 46.1875 5.0312 Q47.5156 5.0312 49.7031 5.8594 L50.25 4.1562 L36.9688 -1.375 L34.7188 -1.375 L34.7188 5.0312 ZM34.7188 8.4531 L34.7188 31.5 Q34.4219 34.8125 32.9609 37.5469 Q31.5 40.2812 29.0781 41.6719 Q26.6562 43.0625 24.3594 43.0625 Q20.0625 43.0625 16.7031 39.2031 Q12.25 34.125 12.25 24.3594 Q12.25 14.5 16.5469 9.25 Q20.8438 4 26.125 4 Q30.5625 4 34.7188 8.4531 Z"
        /><glyph unicode="(" horiz-adv-x="33.30078" d="M31.0625 -19.5781 L31.0625 -21.3906 Q23.6875 -17.6719 18.75 -12.7031 Q11.7188 -5.6094 7.9141 4.0078 Q4.1094 13.625 4.1094 23.9688 Q4.1094 39.1094 11.5781 51.5859 Q19.0469 64.0625 31.0625 69.4375 L31.0625 67.3906 Q25.0469 64.0625 21.1875 58.3047 Q17.3281 52.5469 15.4297 43.7031 Q13.5312 34.8594 13.5312 25.25 Q13.5312 14.7969 15.1406 6.25 Q16.4062 -0.4844 18.2109 -4.5625 Q20.0156 -8.6406 23.0703 -12.3984 Q26.125 -16.1562 31.0625 -19.5781 Z"
        /><glyph unicode="R" horiz-adv-x="66.69922" d="M67.5781 0 L49.9062 0 L27.4844 30.9531 Q25 30.8594 23.4375 30.8594 Q22.7969 30.8594 22.0703 30.8828 Q21.3438 30.9062 20.5625 30.9531 L20.5625 11.7188 Q20.5625 5.4688 21.9219 3.9531 Q23.7812 1.8125 27.4844 1.8125 L30.0781 1.8125 L30.0781 0 L1.7031 0 L1.7031 1.8125 L4.2031 1.8125 Q8.4062 1.8125 10.2031 4.5469 Q11.2344 6.0625 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.75 9.8594 62.25 Q7.9531 64.4062 4.2031 64.4062 L1.7031 64.4062 L1.7031 66.2188 L25.8281 66.2188 Q36.375 66.2188 41.3828 64.6797 Q46.3906 63.1406 49.8828 59.0156 Q53.375 54.8906 53.375 49.1719 Q53.375 43.0625 49.3906 38.5703 Q45.4062 34.0781 37.0625 32.2344 L50.7344 13.2344 Q55.4219 6.6875 58.7891 4.5391 Q62.1562 2.3906 67.5781 1.8125 L67.5781 0 ZM20.5625 34.0312 Q21.4844 34.0312 22.1719 34.0078 Q22.8594 33.9844 23.2969 33.9844 Q32.7656 33.9844 37.5781 38.0859 Q42.3906 42.1875 42.3906 48.5312 Q42.3906 54.7344 38.5078 58.6172 Q34.625 62.5 28.2188 62.5 Q25.3906 62.5 20.5625 61.5781 L20.5625 34.0312 Z"
        /><glyph unicode="S" horiz-adv-x="55.615234" d="M45.8438 67.7188 L45.8438 44.8281 L44.0469 44.8281 Q43.1719 51.4219 40.8984 55.3281 Q38.625 59.2344 34.4219 61.5234 Q30.2188 63.8125 25.7344 63.8125 Q20.6562 63.8125 17.3359 60.7188 Q14.0156 57.625 14.0156 53.6562 Q14.0156 50.6406 16.1094 48.1406 Q19.1406 44.4844 30.5156 38.375 Q39.7969 33.4062 43.1875 30.7422 Q46.5781 28.0781 48.4141 24.4609 Q50.25 20.8438 50.25 16.8906 Q50.25 9.375 44.4141 3.9297 Q38.5781 -1.5156 29.3906 -1.5156 Q26.5156 -1.5156 23.9688 -1.0781 Q22.4688 -0.8281 17.7031 0.7109 Q12.9375 2.25 11.6719 2.25 Q10.4531 2.25 9.7422 1.5156 Q9.0312 0.7812 8.6875 -1.5156 L6.8906 -1.5156 L6.8906 21.1875 L8.6875 21.1875 Q9.9688 14.0625 12.1172 10.5234 Q14.2656 6.9844 18.6797 4.6406 Q23.0938 2.2969 28.375 2.2969 Q34.4688 2.2969 38.0078 5.5156 Q41.5469 8.7344 41.5469 13.1406 Q41.5469 15.5781 40.2109 18.0703 Q38.875 20.5625 36.0312 22.7031 Q34.125 24.1719 25.6328 28.9297 Q17.1406 33.6875 13.5547 36.5234 Q9.9688 39.3594 8.1094 42.7734 Q6.25 46.1875 6.25 50.2969 Q6.25 57.4219 11.7188 62.5703 Q17.1875 67.7188 25.6406 67.7188 Q30.9062 67.7188 36.8125 65.1406 Q39.5469 63.9219 40.6719 63.9219 Q41.9375 63.9219 42.75 64.6797 Q43.5625 65.4375 44.0469 67.7188 L45.8438 67.7188 Z"
        /><glyph unicode="v" horiz-adv-x="50.0" d="M0.8281 44.7344 L21.875 44.7344 L21.875 42.9219 L20.5156 42.9219 Q18.6094 42.9219 17.6094 41.9922 Q16.6094 41.0625 16.6094 39.5 Q16.6094 37.7969 17.625 35.4531 L28.0312 10.75 L38.4844 36.375 Q39.5938 39.1094 39.5938 40.5312 Q39.5938 41.2188 39.2031 41.6562 Q38.6719 42.3906 37.8438 42.6562 Q37.0156 42.9219 34.4688 42.9219 L34.4688 44.7344 L49.0781 44.7344 L49.0781 42.9219 Q46.5312 42.7188 45.5625 41.8906 Q43.8438 40.4375 42.4844 37.0156 L26.6094 -1.375 L24.6094 -1.375 L8.6406 36.375 Q7.5625 39.0156 6.5859 40.1641 Q5.6094 41.3125 4.1094 42.0938 Q3.2656 42.5312 0.8281 42.9219 L0.8281 44.7344 Z"
        /><glyph unicode="z" horiz-adv-x="44.384766" d="M42 13.7188 L41.4531 0 L2 0 L2 1.7656 L31.6875 41.3125 L17.0469 41.3125 Q12.3125 41.3125 10.8438 40.7031 Q9.375 40.0938 8.4531 38.375 Q7.125 35.9375 6.9375 32.3281 L4.9844 32.3281 L5.2812 44.7344 L42.7812 44.7344 L42.7812 42.9219 L12.7969 3.2656 L29.1094 3.2656 Q34.2344 3.2656 36.0625 4.125 Q37.8906 4.9844 39.0156 7.125 Q39.7969 8.6875 40.3281 13.7188 L42 13.7188 Z"
      /></font
    ></defs
    ><g style="fill:white; stroke:white;"
    ><rect x="0" y="0" width="900" style="clip-path:url(#clipPath1); stroke:none;" height="600"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><rect x="0" width="900" height="600" y="0" style="stroke:none;"
      /><path style="stroke:none;" d="M117 534 L815 534 L815 45 L117 45 Z"
    /></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:0.6667;"
    ><line y2="534" style="fill:none;" x1="117" x2="815" y1="534"
      /><line y2="527.02" style="fill:none;" x1="117" x2="117" y1="534"
      /><line y2="527.02" style="fill:none;" x1="271.8504" x2="271.8504" y1="534"
      /><line y2="527.02" style="fill:none;" x1="481.9694" x2="481.9694" y1="534"
      /><line y2="527.02" style="fill:none;" x1="692.0883" x2="692.0883" y1="534"
      /><line y2="527.02" style="fill:none;" x1="815" x2="815" y1="534"
      /><line y2="530.51" style="fill:none;" x1="117" x2="117" y1="534"
      /><line y2="530.51" style="fill:none;" x1="204.2072" x2="204.2072" y1="534"
      /><line y2="530.51" style="fill:none;" x1="271.8504" x2="271.8504" y1="534"
      /><line y2="530.51" style="fill:none;" x1="327.119" x2="327.119" y1="534"
      /><line y2="530.51" style="fill:none;" x1="373.8478" x2="373.8478" y1="534"
      /><line y2="530.51" style="fill:none;" x1="414.3262" x2="414.3262" y1="534"
      /><line y2="530.51" style="fill:none;" x1="450.0306" x2="450.0306" y1="534"
      /><line y2="530.51" style="fill:none;" x1="481.9694" x2="481.9694" y1="534"
      /><line y2="530.51" style="fill:none;" x1="692.0883" x2="692.0883" y1="534"
      /><line y2="530.51" style="fill:none;" x1="815" x2="815" y1="534"
    /></g
    ><g transform="translate(117,539.3333)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-11.5" xml:space="preserve" y="14" style="stroke:none;"
      >300</text
    ></g
    ><g transform="translate(271.8504,539.3333)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-11.5" xml:space="preserve" y="14" style="stroke:none;"
      >500</text
    ></g
    ><g transform="translate(481.9694,539.3333)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-15" xml:space="preserve" y="14" style="stroke:none;"
      >1000</text
    ></g
    ><g transform="translate(692.0883,539.3333)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-15" xml:space="preserve" y="14" style="stroke:none;"
      >2000</text
    ></g
    ><g transform="translate(815,539.3333)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-15" xml:space="preserve" y="14" style="stroke:none;"
      >3000</text
    ></g
    ><g transform="translate(466.0003,557.6667)" style="font-size:18.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-102.5" xml:space="preserve" y="18" style="stroke:none;"
      >Number of sampling points</text
    ></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:0.6667;"
    ><line y2="45" style="fill:none;" x1="117" x2="117" y1="534"
      /><line y2="534" style="fill:none;" x1="117" x2="123.98" y1="534"
      /><line y2="464.1429" style="fill:none;" x1="117" x2="123.98" y1="464.1429"
      /><line y2="394.2857" style="fill:none;" x1="117" x2="123.98" y1="394.2857"
      /><line y2="324.4286" style="fill:none;" x1="117" x2="123.98" y1="324.4286"
      /><line y2="254.5714" style="fill:none;" x1="117" x2="123.98" y1="254.5714"
      /><line y2="184.7143" style="fill:none;" x1="117" x2="123.98" y1="184.7143"
      /><line y2="114.8571" style="fill:none;" x1="117" x2="123.98" y1="114.8571"
      /><line y2="45" style="fill:none;" x1="117" x2="123.98" y1="45"
    /></g
    ><g transform="translate(111.6667,534)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >16.8</text
    ></g
    ><g transform="translate(111.6667,464.1429)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >16.9</text
    ></g
    ><g transform="translate(111.6667,394.2857)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-15" xml:space="preserve" y="5.5" style="stroke:none;"
      >17</text
    ></g
    ><g transform="translate(111.6667,324.4286)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >17.1</text
    ></g
    ><g transform="translate(111.6667,254.5714)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >17.2</text
    ></g
    ><g transform="translate(111.6667,184.7143)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >17.3</text
    ></g
    ><g transform="translate(111.6667,114.8571)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >17.4</text
    ></g
    ><g transform="translate(111.6667,45)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-26" xml:space="preserve" y="5.5" style="stroke:none;"
      >17.5</text
    ></g
    ><g transform="translate(81.6667,289.4998) rotate(-90)" style="font-size:18.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-35.5" xml:space="preserve" y="-4" style="stroke:none;"
      >SNR(dB)</text
    ></g
    ><g style="stroke-linecap:butt; fill:blue; text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:blue; color-interpolation:linearRGB; stroke-width:8;"
    ><path d="M117 475.809 L271.8504 473.5037 L481.9694 424.1846 L692.0883 414.195 L815 407.4887" style="fill:none; fill-rule:evenodd;"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(117,475.809)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(271.8504,473.5037)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(481.9694,424.1846)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(692.0883,414.195)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(815,407.4887)"
      /><path d="M117 279.6501 L271.8504 239.5521 L481.9694 234.732 L692.0883 224.8822 L815 187.1593" style="fill:none; fill-rule:evenodd; stroke:red;"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="fill:red; stroke:none;" transform="translate(117,279.6501)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="fill:red; stroke:none;" transform="translate(271.8504,239.5521)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="fill:red; stroke:none;" transform="translate(481.9694,234.732)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="fill:red; stroke:none;" transform="translate(692.0883,224.8822)"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="fill:red; stroke:none;" transform="translate(815,187.1593)"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><path style="stroke:none;" d="M804 523 L804 454 L523 454 L523 523 Z"
    /></g
    ><g style="text-rendering:geometricPrecision; color-rendering:optimizeQuality; color-interpolation:linearRGB; image-rendering:optimizeQuality;" transform="translate(570,463.9281)"
    ><text x="0" xml:space="preserve" y="5" style="stroke:none;"
      >X polarization with varying sampling counts</text
    ></g
    ><g style="stroke-linecap:butt; fill:blue; text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:blue; color-interpolation:linearRGB; stroke-width:8;"
    ><line y2="463.9281" style="fill:none;" x1="527" x2="567" y1="463.9281"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(547,463.9281)"
    /></g
    ><g style="text-rendering:geometricPrecision; color-rendering:optimizeQuality; color-interpolation:linearRGB; image-rendering:optimizeQuality;" transform="translate(570,480.3094)"
    ><text x="0" xml:space="preserve" y="5" style="stroke:none;"
      >Y polarization with varying sampling counts</text
    ></g
    ><g style="stroke-linecap:butt; fill:red; text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:red; color-interpolation:linearRGB; stroke-width:8;"
    ><line y2="480.3094" style="fill:none;" x1="527" x2="567" y1="480.3094"
      /><path d="M0 -8 C-4.4183 -8 -8 -4.4183 -8 -0 C-8 4.4183 -4.4183 8 -0 8 C4.4183 8 8 4.4183 8 0 L8 0 C8 -4.4183 4.4183 -8 0 -8 Z" style="stroke:none;" transform="translate(547,480.3094)"
    /></g
    ><g style="text-rendering:geometricPrecision; color-rendering:optimizeQuality; color-interpolation:linearRGB; image-rendering:optimizeQuality;" transform="translate(570,496.6906)"
    ><text x="0" xml:space="preserve" y="5" style="stroke:none;"
      >Different step sizes for X polarization</text
    ></g
    ><g style="stroke-linecap:butt; fill:blue; text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:bevel; stroke-dasharray:32,32; stroke:blue; color-interpolation:linearRGB; stroke-width:8; stroke-miterlimit:1;"
    ><line y2="496.6906" style="fill:none;" x1="527" x2="567" y1="496.6906"
      /><path transform="translate(547,496.6906)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 -2 L-2 2 L2 2 L2 -2 Z"
    /></g
    ><g style="text-rendering:geometricPrecision; color-rendering:optimizeQuality; color-interpolation:linearRGB; image-rendering:optimizeQuality;" transform="translate(570,513.072)"
    ><text x="0" xml:space="preserve" y="5" style="stroke:none;"
      >Different step sizes for Y polarization</text
    ></g
    ><g style="stroke-linecap:butt; fill:red; text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:bevel; stroke-dasharray:32,8,16,8; stroke:red; color-interpolation:linearRGB; stroke-width:8; stroke-miterlimit:1;"
    ><line y2="513.072" style="fill:none;" x1="527" x2="567" y1="513.072"
      /><path transform="translate(547,513.072)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 0 L0 2.66 L2 0 L0 -2.66 Z"
    /></g
    ><g style="stroke-linecap:butt; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:0.6667;"
    ><path d="M523 523 L523 454 L804 454 L804 523 Z" style="fill:none; fill-rule:evenodd;"
      /><line x1="117" x2="815" y1="45" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="45"
      /><line x1="117" x2="815" y1="534.0005" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="534.0005"
      /><line x1="117" x2="117" y1="45" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="51.9805"
      /><line x1="291.5" x2="291.5" y1="45" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="51.9805"
      /><line x1="466" x2="466" y1="45" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="51.9805"
      /><line x1="640.5" x2="640.5" y1="45" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="51.9805"
      /><line x1="815" x2="815" y1="45" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="51.9805"
      /><line x1="117" x2="117" y1="534.0005" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="527.02"
      /><line x1="291.5" x2="291.5" y1="534.0005" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="527.02"
      /><line x1="466" x2="466" y1="534.0005" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="527.02"
      /><line x1="640.5" x2="640.5" y1="534.0005" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="527.02"
      /><line x1="815" x2="815" y1="534.0005" style="stroke-linecap:square; fill:none; stroke-linejoin:round;" y2="527.02"
    /></g
    ><g transform="translate(117,39.6663)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="-3" style="stroke:none;"
      >10</text
    ></g
    ><g transform="translate(291.5,39.6663)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="-3" style="stroke:none;"
      >20</text
    ></g
    ><g transform="translate(466,39.6663)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="-3" style="stroke:none;"
      >30</text
    ></g
    ><g transform="translate(640.5,39.6663)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="-3" style="stroke:none;"
      >40</text
    ></g
    ><g transform="translate(815,39.6663)" style="font-size:13.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="-3" style="stroke:none;"
      >50</text
    ></g
    ><g transform="translate(466.0003,21.3336)" style="font-size:18.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-55.5" xml:space="preserve" y="-4" style="stroke:none;"
      >Step Size (mv)</text
    ></g
    ><g style="stroke-linecap:butt; fill:blue; text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:bevel; stroke-dasharray:32,32; stroke:blue; color-interpolation:linearRGB; stroke-width:8; stroke-miterlimit:1;"
    ><path d="M117 467.5661 L291.5 395.7527 L466 366.2729 L640.5 387.8595 L815 410.1428" style="fill:none; fill-rule:evenodd;"
      /><path transform="translate(117,467.5661)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 -2 L-2 2 L2 2 L2 -2 Z"
      /><path transform="translate(291.5,395.7527)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 -2 L-2 2 L2 2 L2 -2 Z"
      /><path transform="translate(466,366.2729)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 -2 L-2 2 L2 2 L2 -2 Z"
      /><path transform="translate(640.5,387.8595)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 -2 L-2 2 L2 2 L2 -2 Z"
      /><path transform="translate(815,410.1428)" style="fill:none; stroke-miterlimit:10; stroke-dasharray:none; stroke-linejoin:miter;" d="M-2 -2 L-2 2 L2 2 L2 -2 Z"
      /><path d="M117 182.0593 L291.5 79.7188 L466 47.8647 L640.5 128.3402 L815 215.8002" style="fill:none; stroke-dasharray:32,8,16,8; fill-rule:evenodd; stroke:red;"
    /></g
    ><g transform="translate(117,182.0593)" style="stroke-linecap:butt; fill:red; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:red; color-interpolation:linearRGB; stroke-width:8;"
    ><path style="fill:none;" d="M-2 0 L0 2.66 L2 0 L0 -2.66 Z"
      /><path d="M-2 0 L0 2.66 L2 0 L0 -2.66 Z" style="fill:none;" transform="translate(174.5,-102.3405)"
      /><path d="M-2 0 L0 2.66 L2 0 L0 -2.66 Z" style="fill:none;" transform="translate(349,-134.1946)"
      /><path d="M-2 0 L0 2.66 L2 0 L0 -2.66 Z" style="fill:none;" transform="translate(523.5,-53.7191)"
      /><path d="M-2 0 L0 2.66 L2 0 L0 -2.66 Z" style="fill:none;" transform="translate(698,33.7408)"
    /></g
  ></g
></svg
>
