<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.0//EN'
          'http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd'>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" style="fill-opacity:1; color-rendering:auto; color-interpolation:auto; text-rendering:auto; stroke:black; stroke-linecap:square; stroke-miterlimit:10; shape-rendering:auto; stroke-opacity:1; fill:black; stroke-dasharray:none; font-weight:normal; stroke-width:1; font-family:'Dialog'; font-style:normal; stroke-linejoin:miter; font-size:12px; stroke-dashoffset:0; image-rendering:auto;" width="1800" height="1200" xmlns="http://www.w3.org/2000/svg"
><!--Generated by the Batik Graphics2D SVG Generator--><defs id="genericDefs"
  /><g
  ><defs id="defs1"
    ><clipPath clipPathUnits="userSpaceOnUse" id="clipPath1"
      ><path d="M0 0 L1800 0 L1800 1200 L0 1200 L0 0 Z"
      /></clipPath
      ><font horiz-adv-x="77.7832" id="font1"
      ><font-face ascent="91.23535" descent="19.506836" units-per-em="100" style="font-style:normal; font-family:Times New Roman; font-weight:normal;"
        /><missing-glyph horiz-adv-x="77.7832" d="M13.875 0 L13.875 62.5 L63.875 62.5 L63.875 0 L13.875 0 ZM15.4375 1.5625 L62.3125 1.5625 L62.3125 60.9375 L15.4375 60.9375 L15.4375 1.5625 Z"
        /><glyph unicode="1" horiz-adv-x="50.0" d="M11.7188 59.7188 L27.8281 67.5781 L29.4375 67.5781 L29.4375 11.6719 Q29.4375 6.1094 29.9062 4.7422 Q30.375 3.375 31.8359 2.6406 Q33.2969 1.9062 37.7969 1.8125 L37.7969 0 L12.8906 0 L12.8906 1.8125 Q17.5781 1.9062 18.9453 2.6172 Q20.3125 3.3281 20.8516 4.5234 Q21.3906 5.7188 21.3906 11.6719 L21.3906 47.4062 Q21.3906 54.6406 20.9062 56.6875 Q20.5625 58.25 19.6562 58.9844 Q18.75 59.7188 17.4844 59.7188 Q15.6719 59.7188 12.4531 58.2031 L11.7188 59.7188 Z"
        /><glyph unicode="3" horiz-adv-x="50.0" d="M5.0781 53.6094 Q7.9062 60.2969 12.2266 63.9375 Q16.5469 67.5781 23 67.5781 Q30.9531 67.5781 35.2031 62.4062 Q38.4219 58.5469 38.4219 54.1562 Q38.4219 46.9219 29.3438 39.2031 Q35.4531 36.8125 38.5781 32.375 Q41.7031 27.9375 41.7031 21.9219 Q41.7031 13.3281 36.2344 7.0312 Q29.1094 -1.1719 15.5781 -1.1719 Q8.8906 -1.1719 6.4688 0.4922 Q4.0469 2.1562 4.0469 4.0469 Q4.0469 5.4688 5.1953 6.5469 Q6.3438 7.625 7.9531 7.625 Q9.1875 7.625 10.4531 7.2344 Q11.2812 6.9844 14.2109 5.4453 Q17.1406 3.9062 18.2656 3.6094 Q20.0625 3.0781 22.125 3.0781 Q27.0938 3.0781 30.7812 6.9375 Q34.4688 10.7969 34.4688 16.0625 Q34.4688 19.9219 32.7656 23.5781 Q31.5 26.3125 29.9844 27.7344 Q27.875 29.6875 24.2188 31.2734 Q20.5625 32.8594 16.75 32.8594 L15.1875 32.8594 L15.1875 34.3281 Q19.0469 34.8125 22.9297 37.1094 Q26.8125 39.4062 28.5703 42.625 Q30.3281 45.8438 30.3281 49.7031 Q30.3281 54.7344 27.1797 57.8359 Q24.0312 60.9375 19.3438 60.9375 Q11.7656 60.9375 6.6875 52.8281 L5.0781 53.6094 Z"
        /><glyph unicode="5" horiz-adv-x="50.0" d="M43.4062 66.2188 L39.5938 57.9062 L19.6719 57.9062 L15.3281 49.0312 Q28.2656 47.125 35.8438 39.4062 Q42.3281 32.7656 42.3281 23.7812 Q42.3281 18.5625 40.2109 14.1172 Q38.0938 9.6719 34.8672 6.5469 Q31.6406 3.4219 27.6875 1.5156 Q22.0781 -1.1719 16.1562 -1.1719 Q10.2031 -1.1719 7.4922 0.8516 Q4.7812 2.875 4.7812 5.3281 Q4.7812 6.6875 5.9062 7.7422 Q7.0312 8.7969 8.7344 8.7969 Q10.0156 8.7969 10.9688 8.4062 Q11.9219 8.0156 14.2031 6.3906 Q17.875 3.8594 21.625 3.8594 Q27.3438 3.8594 31.6641 8.1797 Q35.9844 12.5 35.9844 18.7031 Q35.9844 24.7031 32.125 29.9062 Q28.2656 35.1094 21.4844 37.9375 Q16.1562 40.1406 6.9844 40.4844 L19.6719 66.2188 L43.4062 66.2188 Z"
        /><glyph unicode="7" horiz-adv-x="50.0" d="M10.0625 66.2188 L45.5625 66.2188 L45.5625 64.3594 L23.4844 -1.375 L18.0156 -1.375 L37.7969 58.25 L19.5781 58.25 Q14.0625 58.25 11.7188 56.9375 Q7.625 54.6875 5.125 50 L3.7188 50.5312 L10.0625 66.2188 Z"
        /><glyph unicode="9" horiz-adv-x="50.0" d="M5.2812 -1.375 L5.2812 0.4375 Q11.625 0.5312 17.0938 3.3906 Q22.5625 6.25 27.6641 13.3828 Q32.7656 20.5156 34.7656 29.0469 Q27.0938 24.125 20.9062 24.125 Q13.9219 24.125 8.9375 29.5156 Q3.9531 34.9062 3.9531 43.8438 Q3.9531 52.5469 8.9375 59.3281 Q14.9375 67.5781 24.6094 67.5781 Q32.7656 67.5781 38.5781 60.8438 Q45.7031 52.4844 45.7031 40.2344 Q45.7031 29.2031 40.2812 19.6562 Q34.8594 10.1094 25.2031 3.8125 Q17.3281 -1.375 8.0625 -1.375 L5.2812 -1.375 ZM35.5469 32.6719 Q36.4219 39.0156 36.4219 42.8281 Q36.4219 47.5625 34.8125 53.0547 Q33.2031 58.5469 30.25 61.4766 Q27.2969 64.4062 23.5312 64.4062 Q19.1875 64.4062 15.9141 60.5 Q12.6406 56.5938 12.6406 48.875 Q12.6406 38.5781 17 32.7656 Q20.1719 28.5625 24.8125 28.5625 Q27.0469 28.5625 30.125 29.6406 Q33.2031 30.7188 35.5469 32.6719 Z"
        /><glyph unicode=")" horiz-adv-x="33.30078" d="M2.25 67.3906 L2.25 69.4375 Q9.6719 65.7656 14.5938 60.7969 Q21.5781 53.6562 25.3906 44.0625 Q29.2031 34.4688 29.2031 24.0781 Q29.2031 8.9375 21.7578 -3.5391 Q14.3125 -16.0156 2.25 -21.3906 L2.25 -19.5781 Q8.25 -16.2188 12.1328 -10.4766 Q16.0156 -4.7344 17.8984 4.125 Q19.7812 12.9844 19.7812 22.6094 Q19.7812 33.0156 18.1719 41.6094 Q16.9375 48.3438 15.1094 52.3984 Q13.2812 56.4531 10.2578 60.2109 Q7.2344 63.9688 2.25 67.3906 Z"
        /><glyph unicode="n" horiz-adv-x="50.0" d="M16.1562 36.5781 Q24.0312 46.0469 31.1562 46.0469 Q34.8125 46.0469 37.4531 44.2188 Q40.0938 42.3906 41.6562 38.1875 Q42.7188 35.25 42.7188 29.2031 L42.7188 10.1094 Q42.7188 5.8594 43.4062 4.3438 Q43.9531 3.125 45.1484 2.4453 Q46.3438 1.7656 49.5625 1.7656 L49.5625 0 L27.4375 0 L27.4375 1.7656 L28.375 1.7656 Q31.5 1.7656 32.7422 2.7109 Q33.9844 3.6562 34.4688 5.5156 Q34.6719 6.25 34.6719 10.1094 L34.6719 28.4219 Q34.6719 34.5156 33.0859 37.2812 Q31.5 40.0469 27.7344 40.0469 Q21.9219 40.0469 16.1562 33.6875 L16.1562 10.1094 Q16.1562 5.5625 16.7031 4.5 Q17.3906 3.0781 18.5859 2.4219 Q19.7812 1.7656 23.4375 1.7656 L23.4375 0 L1.3125 0 L1.3125 1.7656 L2.2969 1.7656 Q5.7188 1.7656 6.9141 3.4922 Q8.1094 5.2188 8.1094 10.1094 L8.1094 26.7031 Q8.1094 34.7656 7.7422 36.5234 Q7.375 38.2812 6.6172 38.9141 Q5.8594 39.5469 4.5938 39.5469 Q3.2188 39.5469 1.3125 38.8125 L0.5938 40.5781 L14.0625 46.0469 L16.1562 46.0469 L16.1562 36.5781 Z"
        /><glyph unicode="(" horiz-adv-x="33.30078" d="M31.0625 -19.5781 L31.0625 -21.3906 Q23.6875 -17.6719 18.75 -12.7031 Q11.7188 -5.6094 7.9141 4.0078 Q4.1094 13.625 4.1094 23.9688 Q4.1094 39.1094 11.5781 51.5859 Q19.0469 64.0625 31.0625 69.4375 L31.0625 67.3906 Q25.0469 64.0625 21.1875 58.3047 Q17.3281 52.5469 15.4297 43.7031 Q13.5312 34.8594 13.5312 25.25 Q13.5312 14.7969 15.1406 6.25 Q16.4062 -0.4844 18.2109 -4.5625 Q20.0156 -8.6406 23.0703 -12.3984 Q26.125 -16.1562 31.0625 -19.5781 Z"
        /><glyph unicode=" " horiz-adv-x="25.0" d=""
        /><glyph unicode="e" horiz-adv-x="44.384766" d="M10.6406 27.875 Q10.5938 17.9219 15.4844 12.25 Q20.3594 6.5938 26.9531 6.5938 Q31.3438 6.5938 34.5938 9.0078 Q37.8438 11.4219 40.0469 17.2812 L41.5469 16.3125 Q40.5312 9.625 35.6016 4.125 Q30.6719 -1.375 23.25 -1.375 Q15.1875 -1.375 9.4531 4.9062 Q3.7188 11.1875 3.7188 21.7812 Q3.7188 33.25 9.6016 39.6719 Q15.4844 46.0938 24.3594 46.0938 Q31.8906 46.0938 36.7188 41.1406 Q41.5469 36.1875 41.5469 27.875 L10.6406 27.875 ZM10.6406 30.7188 L31.3438 30.7188 Q31.1094 35.0156 30.3281 36.7656 Q29.1094 39.5 26.6875 41.0625 Q24.2656 42.625 21.625 42.625 Q17.5781 42.625 14.3828 39.4766 Q11.1875 36.3281 10.6406 30.7188 Z"
        /><glyph unicode="m" horiz-adv-x="77.7832" d="M16.4062 36.5312 Q21.2969 41.4062 22.1719 42.1406 Q24.3594 44 26.8984 45.0234 Q29.4375 46.0469 31.9375 46.0469 Q36.1406 46.0469 39.1641 43.6016 Q42.1875 41.1562 43.2188 36.5312 Q48.25 42.3906 51.7109 44.2188 Q55.1719 46.0469 58.8438 46.0469 Q62.4062 46.0469 65.1641 44.2188 Q67.9219 42.3906 69.5312 38.2344 Q70.6094 35.4062 70.6094 29.3438 L70.6094 10.1094 Q70.6094 5.9062 71.2344 4.3438 Q71.7344 3.2656 73.0469 2.5156 Q74.3594 1.7656 77.3438 1.7656 L77.3438 0 L55.2812 0 L55.2812 1.7656 L56.2031 1.7656 Q59.0781 1.7656 60.6875 2.875 Q61.8125 3.6562 62.3125 5.375 Q62.5 6.2031 62.5 10.1094 L62.5 29.3438 Q62.5 34.8125 61.1875 37.0625 Q59.2812 40.1875 55.0781 40.1875 Q52.4844 40.1875 49.875 38.8906 Q47.2656 37.5938 43.5625 34.0781 L43.4531 33.5469 L43.5625 31.4531 L43.5625 10.1094 Q43.5625 5.5156 44.0703 4.3906 Q44.5781 3.2656 45.9922 2.5156 Q47.4062 1.7656 50.8281 1.7656 L50.8281 0 L28.2188 0 L28.2188 1.7656 Q31.9375 1.7656 33.3281 2.6406 Q34.7188 3.5156 35.25 5.2812 Q35.5 6.1094 35.5 10.1094 L35.5 29.3438 Q35.5 34.8125 33.8906 37.2031 Q31.7344 40.3281 27.875 40.3281 Q25.25 40.3281 22.6562 38.9219 Q18.6094 36.7656 16.4062 34.0781 L16.4062 10.1094 Q16.4062 5.7188 17.0156 4.3984 Q17.625 3.0781 18.8203 2.4219 Q20.0156 1.7656 23.6875 1.7656 L23.6875 0 L1.5625 0 L1.5625 1.7656 Q4.6406 1.7656 5.8594 2.4219 Q7.0781 3.0781 7.7109 4.5156 Q8.3438 5.9531 8.3438 10.1094 L8.3438 27.2031 Q8.3438 34.5781 7.9062 36.7188 Q7.5625 38.3281 6.8359 38.9375 Q6.1094 39.5469 4.8281 39.5469 Q3.4688 39.5469 1.5625 38.8125 L0.8281 40.5781 L14.3125 46.0469 L16.4062 46.0469 L16.4062 36.5312 Z"
        /><glyph unicode="i" horiz-adv-x="27.783203" d="M14.5 69.4375 Q16.5469 69.4375 17.9922 67.9922 Q19.4375 66.5469 19.4375 64.5 Q19.4375 62.4531 17.9922 60.9844 Q16.5469 59.5156 14.5 59.5156 Q12.4531 59.5156 10.9844 60.9844 Q9.5156 62.4531 9.5156 64.5 Q9.5156 66.5469 10.9609 67.9922 Q12.4062 69.4375 14.5 69.4375 ZM18.5625 46.0469 L18.5625 10.1094 Q18.5625 5.9062 19.1719 4.5156 Q19.7812 3.125 20.9766 2.4453 Q22.1719 1.7656 25.3438 1.7656 L25.3438 0 L3.6094 0 L3.6094 1.7656 Q6.8906 1.7656 8.0078 2.3984 Q9.125 3.0312 9.7891 4.4922 Q10.4531 5.9531 10.4531 10.1094 L10.4531 27.3438 Q10.4531 34.625 10.0156 36.7656 Q9.6719 38.3281 8.9375 38.9375 Q8.2031 39.5469 6.9375 39.5469 Q5.5625 39.5469 3.6094 38.8125 L2.9375 40.5781 L16.4062 46.0469 L18.5625 46.0469 Z"
        /><glyph unicode="T" horiz-adv-x="61.083984" d="M57.8594 66.2188 L58.5938 50.6875 L56.7344 50.6875 Q56.2031 54.7812 55.2812 56.5469 Q53.7656 59.375 51.25 60.7188 Q48.7344 62.0625 44.625 62.0625 L35.2969 62.0625 L35.2969 11.4688 Q35.2969 5.375 36.625 3.8594 Q38.4844 1.8125 42.3281 1.8125 L44.625 1.8125 L44.625 0 L16.5469 0 L16.5469 1.8125 L18.8906 1.8125 Q23.0938 1.8125 24.8594 4.3438 Q25.9219 5.9062 25.9219 11.4688 L25.9219 62.0625 L17.9688 62.0625 Q13.3281 62.0625 11.375 61.375 Q8.8438 60.4531 7.0312 57.8125 Q5.2188 55.1719 4.8906 50.6875 L3.0312 50.6875 L3.8125 66.2188 L57.8594 66.2188 Z"
        /><glyph unicode="6" horiz-adv-x="50.0" d="M44.8281 67.5781 L44.8281 65.7656 Q38.375 65.1406 34.2969 63.2109 Q30.2188 61.2812 26.2422 57.3281 Q22.2656 53.375 19.6562 48.5156 Q17.0469 43.6562 15.2812 36.9688 Q22.3125 41.7969 29.3906 41.7969 Q36.1875 41.7969 41.1641 36.3281 Q46.1406 30.8594 46.1406 22.2656 Q46.1406 13.9688 41.1094 7.125 Q35.0625 -1.1719 25.0938 -1.1719 Q18.3125 -1.1719 13.5781 3.3281 Q4.2969 12.0625 4.2969 25.9844 Q4.2969 34.8594 7.8594 42.8672 Q11.4219 50.875 18.0391 57.0781 Q24.6562 63.2812 30.7109 65.4297 Q36.7656 67.5781 42 67.5781 L44.8281 67.5781 ZM14.4531 33.4062 Q13.5781 26.8125 13.5781 22.75 Q13.5781 18.0625 15.3125 12.5703 Q17.0469 7.0781 20.4531 3.8594 Q22.9531 1.5625 26.5156 1.5625 Q30.7656 1.5625 34.1094 5.5703 Q37.4531 9.5781 37.4531 17 Q37.4531 25.3438 34.1328 31.4453 Q30.8125 37.5469 24.7031 37.5469 Q22.8594 37.5469 20.7344 36.7656 Q18.6094 35.9844 14.4531 33.4062 Z"
        /><glyph unicode="." horiz-adv-x="25.0" d="M12.5 9.4688 Q14.7969 9.4688 16.3594 7.8828 Q17.9219 6.2969 17.9219 4.0469 Q17.9219 1.8125 16.3359 0.2188 Q14.75 -1.375 12.5 -1.375 Q10.25 -1.375 8.6641 0.2188 Q7.0781 1.8125 7.0781 4.0469 Q7.0781 6.3438 8.6641 7.9062 Q10.25 9.4688 12.5 9.4688 Z"
        /><glyph unicode="2" horiz-adv-x="50.0" d="M45.8438 12.75 L41.2188 0 L2.1562 0 L2.1562 1.8125 Q19.3906 17.5312 26.4219 27.4922 Q33.4531 37.4531 33.4531 45.7031 Q33.4531 52 29.5938 56.0547 Q25.7344 60.1094 20.3594 60.1094 Q15.4844 60.1094 11.6016 57.25 Q7.7188 54.3906 5.8594 48.875 L4.0469 48.875 Q5.2812 57.9062 10.3281 62.7422 Q15.375 67.5781 22.9531 67.5781 Q31 67.5781 36.3984 62.4062 Q41.7969 57.2344 41.7969 50.2031 Q41.7969 45.1719 39.4531 40.1406 Q35.8438 32.2344 27.7344 23.3906 Q15.5781 10.1094 12.5469 7.375 L29.8281 7.375 Q35.1094 7.375 37.2344 7.7656 Q39.3594 8.1562 41.0703 9.3516 Q42.7812 10.5469 44.0469 12.75 L45.8438 12.75 Z"
        /><glyph unicode="4" horiz-adv-x="50.0" d="M46.5312 24.4219 L46.5312 17.4844 L37.6406 17.4844 L37.6406 0 L29.5938 0 L29.5938 17.4844 L1.5625 17.4844 L1.5625 23.7344 L32.2812 67.5781 L37.6406 67.5781 L37.6406 24.4219 L46.5312 24.4219 ZM29.5938 24.4219 L29.5938 57.2812 L6.3438 24.4219 L29.5938 24.4219 Z"
        /><glyph unicode="8" horiz-adv-x="50.0" d="M19.1875 33.3438 Q11.3281 39.7969 9.0547 43.7031 Q6.7812 47.6094 6.7812 51.8125 Q6.7812 58.25 11.7656 62.9141 Q16.75 67.5781 25 67.5781 Q33.0156 67.5781 37.8984 63.2344 Q42.7812 58.8906 42.7812 53.3281 Q42.7812 49.6094 40.1406 45.75 Q37.5 41.8906 29.1562 36.6719 Q37.75 30.0312 40.5312 26.2188 Q44.2344 21.2344 44.2344 15.7188 Q44.2344 8.7344 38.9141 3.7812 Q33.5938 -1.1719 24.9531 -1.1719 Q15.5312 -1.1719 10.25 4.7344 Q6.0625 9.4688 6.0625 15.0938 Q6.0625 19.4844 9.0156 23.8047 Q11.9688 28.125 19.1875 33.3438 ZM26.8594 38.5781 Q32.7188 43.8438 34.2812 46.8984 Q35.8438 49.9531 35.8438 53.8125 Q35.8438 58.9375 32.9609 61.8438 Q30.0781 64.75 25.0938 64.75 Q20.125 64.75 17 61.8672 Q13.875 58.9844 13.875 55.125 Q13.875 52.5938 15.1641 50.0547 Q16.4531 47.5156 18.8438 45.2188 L26.8594 38.5781 ZM21.4844 31.5 Q17.4375 28.0781 15.4844 24.0469 Q13.5312 20.0156 13.5312 15.3281 Q13.5312 9.0312 16.9688 5.25 Q20.4062 1.4688 25.7344 1.4688 Q31 1.4688 34.1797 4.4453 Q37.3594 7.4219 37.3594 11.6719 Q37.3594 15.1875 35.5 17.9688 Q32.0312 23.1406 21.4844 31.5 Z"
        /><glyph unicode="B" horiz-adv-x="66.69922" d="M46.1875 33.7969 Q53.0781 32.3281 56.5 29.1094 Q61.2344 24.6094 61.2344 18.1094 Q61.2344 13.1875 58.1094 8.6719 Q54.9844 4.1562 49.5391 2.0781 Q44.0938 0 32.9062 0 L1.6562 0 L1.6562 1.8125 L4.1562 1.8125 Q8.2969 1.8125 10.1094 4.4375 Q11.2344 6.1562 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.6406 9.8125 62.25 Q7.9062 64.4062 4.1562 64.4062 L1.6562 64.4062 L1.6562 66.2188 L30.2812 66.2188 Q38.2812 66.2188 43.1094 65.0469 Q50.4375 63.2812 54.2969 58.8125 Q58.1562 54.3438 58.1562 48.5312 Q58.1562 43.5625 55.125 39.625 Q52.0938 35.6875 46.1875 33.7969 ZM20.6094 36.4219 Q22.4062 36.0781 24.7266 35.9141 Q27.0469 35.75 29.8281 35.75 Q36.9688 35.75 40.5547 37.2812 Q44.1406 38.8125 46.0469 41.9922 Q47.9531 45.1719 47.9531 48.9219 Q47.9531 54.7344 43.2188 58.8359 Q38.4844 62.9375 29.3906 62.9375 Q24.5156 62.9375 20.6094 61.8594 L20.6094 36.4219 ZM20.6094 4.7812 Q26.2656 3.4688 31.7812 3.4688 Q40.625 3.4688 45.2656 7.4453 Q49.9062 11.4219 49.9062 17.2812 Q49.9062 21.1406 47.8047 24.7031 Q45.7031 28.2656 40.9688 30.3203 Q36.2344 32.375 29.25 32.375 Q26.2188 32.375 24.0703 32.2734 Q21.9219 32.1719 20.6094 31.9375 L20.6094 4.7812 Z"
        /><glyph unicode="d" horiz-adv-x="50.0" d="M34.7188 5.0312 Q31.4531 1.6094 28.3281 0.1172 Q25.2031 -1.375 21.5781 -1.375 Q14.2656 -1.375 8.7969 4.7578 Q3.3281 10.8906 3.3281 20.5156 Q3.3281 30.125 9.3828 38.1094 Q15.4375 46.0938 24.9531 46.0938 Q30.8594 46.0938 34.7188 42.3281 L34.7188 50.5938 Q34.7188 58.25 34.3516 60.0078 Q33.9844 61.7656 33.2031 62.3984 Q32.4219 63.0312 31.25 63.0312 Q29.9844 63.0312 27.875 62.25 L27.25 63.9688 L40.5781 69.4375 L42.7812 69.4375 L42.7812 17.7188 Q42.7812 9.8594 43.1406 8.125 Q43.5 6.3906 44.3125 5.7109 Q45.125 5.0312 46.1875 5.0312 Q47.5156 5.0312 49.7031 5.8594 L50.25 4.1562 L36.9688 -1.375 L34.7188 -1.375 L34.7188 5.0312 ZM34.7188 8.4531 L34.7188 31.5 Q34.4219 34.8125 32.9609 37.5469 Q31.5 40.2812 29.0781 41.6719 Q26.6562 43.0625 24.3594 43.0625 Q20.0625 43.0625 16.7031 39.2031 Q12.25 34.125 12.25 24.3594 Q12.25 14.5 16.5469 9.25 Q20.8438 4 26.125 4 Q30.5625 4 34.7188 8.4531 Z"
        /><glyph unicode="R" horiz-adv-x="66.69922" d="M67.5781 0 L49.9062 0 L27.4844 30.9531 Q25 30.8594 23.4375 30.8594 Q22.7969 30.8594 22.0703 30.8828 Q21.3438 30.9062 20.5625 30.9531 L20.5625 11.7188 Q20.5625 5.4688 21.9219 3.9531 Q23.7812 1.8125 27.4844 1.8125 L30.0781 1.8125 L30.0781 0 L1.7031 0 L1.7031 1.8125 L4.2031 1.8125 Q8.4062 1.8125 10.2031 4.5469 Q11.2344 6.0625 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.75 9.8594 62.25 Q7.9531 64.4062 4.2031 64.4062 L1.7031 64.4062 L1.7031 66.2188 L25.8281 66.2188 Q36.375 66.2188 41.3828 64.6797 Q46.3906 63.1406 49.8828 59.0156 Q53.375 54.8906 53.375 49.1719 Q53.375 43.0625 49.3906 38.5703 Q45.4062 34.0781 37.0625 32.2344 L50.7344 13.2344 Q55.4219 6.6875 58.7891 4.5391 Q62.1562 2.3906 67.5781 1.8125 L67.5781 0 ZM20.5625 34.0312 Q21.4844 34.0312 22.1719 34.0078 Q22.8594 33.9844 23.2969 33.9844 Q32.7656 33.9844 37.5781 38.0859 Q42.3906 42.1875 42.3906 48.5312 Q42.3906 54.7344 38.5078 58.6172 Q34.625 62.5 28.2188 62.5 Q25.3906 62.5 20.5625 61.5781 L20.5625 34.0312 Z"
        /><glyph unicode="N" horiz-adv-x="72.2168" d="M-1.3125 66.2188 L16.6562 66.2188 L57.125 16.5469 L57.125 54.7344 Q57.125 60.8438 55.7656 62.3594 Q53.9531 64.4062 50.0469 64.4062 L47.75 64.4062 L47.75 66.2188 L70.7969 66.2188 L70.7969 64.4062 L68.4531 64.4062 Q64.2656 64.4062 62.5 61.8594 Q61.4219 60.2969 61.4219 54.7344 L61.4219 -1.0781 L59.6719 -1.0781 L16.0156 52.25 L16.0156 11.4688 Q16.0156 5.375 17.3281 3.8594 Q19.1875 1.8125 23.0469 1.8125 L25.3906 1.8125 L25.3906 0 L2.3438 0 L2.3438 1.8125 L4.6406 1.8125 Q8.8906 1.8125 10.6406 4.3438 Q11.7188 5.9062 11.7188 11.4688 L11.7188 57.5156 Q8.8438 60.8906 7.3516 61.9609 Q5.8594 63.0312 2.9844 63.9688 Q1.5625 64.4062 -1.3125 64.4062 L-1.3125 66.2188 Z"
        /><glyph unicode="S" horiz-adv-x="55.615234" d="M45.8438 67.7188 L45.8438 44.8281 L44.0469 44.8281 Q43.1719 51.4219 40.8984 55.3281 Q38.625 59.2344 34.4219 61.5234 Q30.2188 63.8125 25.7344 63.8125 Q20.6562 63.8125 17.3359 60.7188 Q14.0156 57.625 14.0156 53.6562 Q14.0156 50.6406 16.1094 48.1406 Q19.1406 44.4844 30.5156 38.375 Q39.7969 33.4062 43.1875 30.7422 Q46.5781 28.0781 48.4141 24.4609 Q50.25 20.8438 50.25 16.8906 Q50.25 9.375 44.4141 3.9297 Q38.5781 -1.5156 29.3906 -1.5156 Q26.5156 -1.5156 23.9688 -1.0781 Q22.4688 -0.8281 17.7031 0.7109 Q12.9375 2.25 11.6719 2.25 Q10.4531 2.25 9.7422 1.5156 Q9.0312 0.7812 8.6875 -1.5156 L6.8906 -1.5156 L6.8906 21.1875 L8.6875 21.1875 Q9.9688 14.0625 12.1172 10.5234 Q14.2656 6.9844 18.6797 4.6406 Q23.0938 2.2969 28.375 2.2969 Q34.4688 2.2969 38.0078 5.5156 Q41.5469 8.7344 41.5469 13.1406 Q41.5469 15.5781 40.2109 18.0703 Q38.875 20.5625 36.0312 22.7031 Q34.125 24.1719 25.6328 28.9297 Q17.1406 33.6875 13.5547 36.5234 Q9.9688 39.3594 8.1094 42.7734 Q6.25 46.1875 6.25 50.2969 Q6.25 57.4219 11.7188 62.5703 Q17.1875 67.7188 25.6406 67.7188 Q30.9062 67.7188 36.8125 65.1406 Q39.5469 63.9219 40.6719 63.9219 Q41.9375 63.9219 42.75 64.6797 Q43.5625 65.4375 44.0469 67.7188 L45.8438 67.7188 Z"
        /><glyph unicode="r" horiz-adv-x="33.30078" d="M16.2188 46.0469 L16.2188 35.9844 Q21.8281 46.0469 27.7344 46.0469 Q30.4219 46.0469 32.1797 44.4141 Q33.9375 42.7812 33.9375 40.625 Q33.9375 38.7188 32.6641 37.3984 Q31.3906 36.0781 29.6406 36.0781 Q27.9375 36.0781 25.8125 37.7656 Q23.6875 39.4531 22.6562 39.4531 Q21.7812 39.4531 20.75 38.4844 Q18.5625 36.4688 16.2188 31.8906 L16.2188 10.4531 Q16.2188 6.7344 17.1406 4.8281 Q17.7812 3.5156 19.3906 2.6406 Q21 1.7656 24.0312 1.7656 L24.0312 0 L1.125 0 L1.125 1.7656 Q4.5469 1.7656 6.2031 2.8281 Q7.4219 3.6094 7.9062 5.3281 Q8.1562 6.1562 8.1562 10.0625 L8.1562 27.3906 Q8.1562 35.2031 7.8359 36.6953 Q7.5156 38.1875 6.6641 38.8672 Q5.8125 39.5469 4.5469 39.5469 Q3.0312 39.5469 1.125 38.8125 L0.6406 40.5781 L14.1562 46.0469 L16.2188 46.0469 Z"
        /><glyph unicode="b" horiz-adv-x="50.0" d="M15.375 37.0156 Q21.875 46.0469 29.3906 46.0469 Q36.2812 46.0469 41.4062 40.1641 Q46.5312 34.2812 46.5312 24.0781 Q46.5312 12.1562 38.625 4.8906 Q31.8438 -1.375 23.4844 -1.375 Q19.5781 -1.375 15.5547 0.0469 Q11.5312 1.4688 7.3281 4.2969 L7.3281 50.6406 Q7.3281 58.25 6.9609 60.0078 Q6.5938 61.7656 5.8125 62.3984 Q5.0312 63.0312 3.8594 63.0312 Q2.4844 63.0312 0.4375 62.25 L-0.25 63.9688 L13.1875 69.4375 L15.375 69.4375 L15.375 37.0156 ZM15.375 33.8906 L15.375 7.125 Q17.875 4.6875 20.5312 3.4453 Q23.1875 2.2031 25.9844 2.2031 Q30.4219 2.2031 34.2578 7.0859 Q38.0938 11.9688 38.0938 21.2969 Q38.0938 29.8906 34.2578 34.5 Q30.4219 39.1094 25.5312 39.1094 Q22.9531 39.1094 20.3594 37.7969 Q18.4062 36.8125 15.375 33.8906 Z"
        /><glyph unicode="F" horiz-adv-x="55.615234" d="M20.4531 62.5938 L20.4531 36.5781 L32.5156 36.5781 Q36.6719 36.5781 38.6016 38.4062 Q40.5312 40.2344 41.1562 45.6562 L42.9688 45.6562 L42.9688 23.25 L41.1562 23.25 Q41.1094 27.0938 40.1562 28.9062 Q39.2031 30.7188 37.5234 31.6172 Q35.8438 32.5156 32.5156 32.5156 L20.4531 32.5156 L20.4531 11.7188 Q20.4531 6.6875 21.0938 5.0781 Q21.5781 3.8594 23.1406 2.9844 Q25.2969 1.8125 27.6406 1.8125 L30.0312 1.8125 L30.0312 0 L1.6094 0 L1.6094 1.8125 L3.9531 1.8125 Q8.0625 1.8125 9.9062 4.2031 Q11.0781 5.7656 11.0781 11.7188 L11.0781 54.5 Q11.0781 59.5156 10.4531 61.1406 Q9.9688 62.3594 8.4531 63.2344 Q6.3438 64.4062 3.9531 64.4062 L1.6094 64.4062 L1.6094 66.2188 L50.875 66.2188 L51.5156 51.6562 L49.8125 51.6562 Q48.5312 56.2969 46.8516 58.4688 Q45.1719 60.6406 42.7031 61.6172 Q40.2344 62.5938 35.0625 62.5938 L20.4531 62.5938 Z"
      /></font
    ></defs
    ><g style="fill:white; stroke:white;"
    ><rect x="0" y="0" width="1800" style="clip-path:url(#clipPath1); stroke:none;" height="1200"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><rect x="0" width="1800" height="1200" y="0" style="stroke:none;"
      /><path style="stroke:none;" d="M234 1068 L1629 1068 L1629 90 L234 90 Z"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="90" style="fill:none;" x1="284.3351" x2="284.3351" y1="1068"
      /><line y2="90" style="fill:none;" x1="320.2887" x2="320.2887" y1="1068"
      /><line y2="90" style="fill:none;" x1="356.2423" x2="356.2423" y1="1068"
      /><line y2="90" style="fill:none;" x1="428.1495" x2="428.1495" y1="1068"
      /><line y2="90" style="fill:none;" x1="464.1031" x2="464.1031" y1="1068"
      /><line y2="90" style="fill:none;" x1="500.0567" x2="500.0567" y1="1068"
      /><line y2="90" style="fill:none;" x1="571.9639" x2="571.9639" y1="1068"
      /><line y2="90" style="fill:none;" x1="607.9175" x2="607.9175" y1="1068"
      /><line y2="90" style="fill:none;" x1="643.8712" x2="643.8712" y1="1068"
      /><line y2="90" style="fill:none;" x1="715.7783" x2="715.7783" y1="1068"
      /><line y2="90" style="fill:none;" x1="751.7319" x2="751.7319" y1="1068"
      /><line y2="90" style="fill:none;" x1="787.6855" x2="787.6855" y1="1068"
      /><line y2="90" style="fill:none;" x1="859.5928" x2="859.5928" y1="1068"
      /><line y2="90" style="fill:none;" x1="895.5464" x2="895.5464" y1="1068"
      /><line y2="90" style="fill:none;" x1="931.5" x2="931.5" y1="1068"
      /><line y2="90" style="fill:none;" x1="1003.4072" x2="1003.4072" y1="1068"
      /><line y2="90" style="fill:none;" x1="1039.3608" x2="1039.3608" y1="1068"
      /><line y2="90" style="fill:none;" x1="1075.3145" x2="1075.3145" y1="1068"
      /><line y2="90" style="fill:none;" x1="1147.2217" x2="1147.2217" y1="1068"
      /><line y2="90" style="fill:none;" x1="1183.1753" x2="1183.1753" y1="1068"
      /><line y2="90" style="fill:none;" x1="1219.1289" x2="1219.1289" y1="1068"
      /><line y2="90" style="fill:none;" x1="1291.0361" x2="1291.0361" y1="1068"
      /><line y2="90" style="fill:none;" x1="1326.9897" x2="1326.9897" y1="1068"
      /><line y2="90" style="fill:none;" x1="1362.9434" x2="1362.9434" y1="1068"
      /><line y2="90" style="fill:none;" x1="1434.8505" x2="1434.8505" y1="1068"
      /><line y2="90" style="fill:none;" x1="1470.8041" x2="1470.8041" y1="1068"
      /><line y2="90" style="fill:none;" x1="1506.7577" x2="1506.7577" y1="1068"
      /><line y2="90" style="fill:none;" x1="1578.6649" x2="1578.6649" y1="1068"
      /><line y2="90" style="fill:none;" x1="1614.6185" x2="1614.6185" y1="1068"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="90" style="fill:none;" x1="248.3814" x2="248.3814" y1="1068"
      /><line y2="90" style="fill:none;" x1="392.1959" x2="392.1959" y1="1068"
      /><line y2="90" style="fill:none;" x1="536.0103" x2="536.0103" y1="1068"
      /><line y2="90" style="fill:none;" x1="679.8248" x2="679.8248" y1="1068"
      /><line y2="90" style="fill:none;" x1="823.6392" x2="823.6392" y1="1068"
      /><line y2="90" style="fill:none;" x1="967.4536" x2="967.4536" y1="1068"
      /><line y2="90" style="fill:none;" x1="1111.2681" x2="1111.2681" y1="1068"
      /><line y2="90" style="fill:none;" x1="1255.0825" x2="1255.0825" y1="1068"
      /><line y2="90" style="fill:none;" x1="1398.8969" x2="1398.8969" y1="1068"
      /><line y2="90" style="fill:none;" x1="1542.7113" x2="1542.7113" y1="1068"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="1054.9597" style="fill:none;" x1="1629" x2="234" y1="1054.9597"
      /><line y2="1041.9194" style="fill:none;" x1="1629" x2="234" y1="1041.9194"
      /><line y2="1028.8804" style="fill:none;" x1="1629" x2="234" y1="1028.8804"
      /><line y2="1015.84" style="fill:none;" x1="1629" x2="234" y1="1015.84"
      /><line y2="989.7595" style="fill:none;" x1="1629" x2="234" y1="989.7595"
      /><line y2="976.7204" style="fill:none;" x1="1629" x2="234" y1="976.7204"
      /><line y2="963.6801" style="fill:none;" x1="1629" x2="234" y1="963.6801"
      /><line y2="950.6398" style="fill:none;" x1="1629" x2="234" y1="950.6398"
      /><line y2="924.5604" style="fill:none;" x1="1629" x2="234" y1="924.5604"
      /><line y2="911.5201" style="fill:none;" x1="1629" x2="234" y1="911.5201"
      /><line y2="898.4799" style="fill:none;" x1="1629" x2="234" y1="898.4799"
      /><line y2="885.4396" style="fill:none;" x1="1629" x2="234" y1="885.4396"
      /><line y2="859.3602" style="fill:none;" x1="1629" x2="234" y1="859.3602"
      /><line y2="846.3199" style="fill:none;" x1="1629" x2="234" y1="846.3199"
      /><line y2="833.2796" style="fill:none;" x1="1629" x2="234" y1="833.2796"
      /><line y2="820.2405" style="fill:none;" x1="1629" x2="234" y1="820.2405"
      /><line y2="794.16" style="fill:none;" x1="1629" x2="234" y1="794.16"
      /><line y2="781.1196" style="fill:none;" x1="1629" x2="234" y1="781.1196"
      /><line y2="768.0806" style="fill:none;" x1="1629" x2="234" y1="768.0806"
      /><line y2="755.0403" style="fill:none;" x1="1629" x2="234" y1="755.0403"
      /><line y2="728.9597" style="fill:none;" x1="1629" x2="234" y1="728.9597"
      /><line y2="715.9194" style="fill:none;" x1="1629" x2="234" y1="715.9194"
      /><line y2="702.8804" style="fill:none;" x1="1629" x2="234" y1="702.8804"
      /><line y2="689.84" style="fill:none;" x1="1629" x2="234" y1="689.84"
      /><line y2="663.7595" style="fill:none;" x1="1629" x2="234" y1="663.7595"
      /><line y2="650.7204" style="fill:none;" x1="1629" x2="234" y1="650.7204"
      /><line y2="637.6801" style="fill:none;" x1="1629" x2="234" y1="637.6801"
      /><line y2="624.6398" style="fill:none;" x1="1629" x2="234" y1="624.6398"
      /><line y2="598.5604" style="fill:none;" x1="1629" x2="234" y1="598.5604"
      /><line y2="585.5201" style="fill:none;" x1="1629" x2="234" y1="585.5201"
      /><line y2="572.4799" style="fill:none;" x1="1629" x2="234" y1="572.4799"
      /><line y2="559.4396" style="fill:none;" x1="1629" x2="234" y1="559.4396"
      /><line y2="533.3602" style="fill:none;" x1="1629" x2="234" y1="533.3602"
      /><line y2="520.3199" style="fill:none;" x1="1629" x2="234" y1="520.3199"
      /><line y2="507.2796" style="fill:none;" x1="1629" x2="234" y1="507.2796"
      /><line y2="494.2405" style="fill:none;" x1="1629" x2="234" y1="494.2405"
      /><line y2="468.1599" style="fill:none;" x1="1629" x2="234" y1="468.1599"
      /><line y2="455.1197" style="fill:none;" x1="1629" x2="234" y1="455.1197"
      /><line y2="442.0806" style="fill:none;" x1="1629" x2="234" y1="442.0806"
      /><line y2="429.0403" style="fill:none;" x1="1629" x2="234" y1="429.0403"
      /><line y2="402.9597" style="fill:none;" x1="1629" x2="234" y1="402.9597"
      /><line y2="389.9194" style="fill:none;" x1="1629" x2="234" y1="389.9194"
      /><line y2="376.8803" style="fill:none;" x1="1629" x2="234" y1="376.8803"
      /><line y2="363.8401" style="fill:none;" x1="1629" x2="234" y1="363.8401"
      /><line y2="337.7595" style="fill:none;" x1="1629" x2="234" y1="337.7595"
      /><line y2="324.7204" style="fill:none;" x1="1629" x2="234" y1="324.7204"
      /><line y2="311.6801" style="fill:none;" x1="1629" x2="234" y1="311.6801"
      /><line y2="298.6398" style="fill:none;" x1="1629" x2="234" y1="298.6398"
      /><line y2="272.5605" style="fill:none;" x1="1629" x2="234" y1="272.5605"
      /><line y2="259.5201" style="fill:none;" x1="1629" x2="234" y1="259.5201"
      /><line y2="246.4799" style="fill:none;" x1="1629" x2="234" y1="246.4799"
      /><line y2="233.4395" style="fill:none;" x1="1629" x2="234" y1="233.4395"
      /><line y2="207.3602" style="fill:none;" x1="1629" x2="234" y1="207.3602"
      /><line y2="194.3199" style="fill:none;" x1="1629" x2="234" y1="194.3199"
      /><line y2="181.2796" style="fill:none;" x1="1629" x2="234" y1="181.2796"
      /><line y2="168.2405" style="fill:none;" x1="1629" x2="234" y1="168.2405"
      /><line y2="142.1599" style="fill:none;" x1="1629" x2="234" y1="142.1599"
      /><line y2="129.1196" style="fill:none;" x1="1629" x2="234" y1="129.1196"
      /><line y2="116.0806" style="fill:none;" x1="1629" x2="234" y1="116.0806"
      /><line y2="103.0403" style="fill:none;" x1="1629" x2="234" y1="103.0403"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="1068" style="fill:none;" x1="1629" x2="234" y1="1068"
      /><line y2="1002.7997" style="fill:none;" x1="1629" x2="234" y1="1002.7997"
      /><line y2="937.5995" style="fill:none;" x1="1629" x2="234" y1="937.5995"
      /><line y2="872.4005" style="fill:none;" x1="1629" x2="234" y1="872.4005"
      /><line y2="807.2003" style="fill:none;" x1="1629" x2="234" y1="807.2003"
      /><line y2="742" style="fill:none;" x1="1629" x2="234" y1="742"
      /><line y2="676.7997" style="fill:none;" x1="1629" x2="234" y1="676.7997"
      /><line y2="611.5995" style="fill:none;" x1="1629" x2="234" y1="611.5995"
      /><line y2="546.4005" style="fill:none;" x1="1629" x2="234" y1="546.4005"
      /><line y2="481.2003" style="fill:none;" x1="1629" x2="234" y1="481.2003"
      /><line y2="416" style="fill:none;" x1="1629" x2="234" y1="416"
      /><line y2="350.7997" style="fill:none;" x1="1629" x2="234" y1="350.7997"
      /><line y2="285.5995" style="fill:none;" x1="1629" x2="234" y1="285.5995"
      /><line y2="220.4005" style="fill:none;" x1="1629" x2="234" y1="220.4005"
      /><line y2="155.2003" style="fill:none;" x1="1629" x2="234" y1="155.2003"
      /><line y2="90" style="fill:none;" x1="1629" x2="234" y1="90"
      /><line x1="234" x2="1629" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1068"
      /><line x1="234" x2="1629" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="90"
      /><line x1="248.3814" x2="248.3814" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="392.1959" x2="392.1959" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="536.0103" x2="536.0103" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="679.8248" x2="679.8248" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="823.6392" x2="823.6392" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="967.4536" x2="967.4536" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="1111.2681" x2="1111.2681" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="1255.0825" x2="1255.0825" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="1398.8969" x2="1398.8969" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="1542.7113" x2="1542.7113" y1="1068" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1054.0494"
      /><line x1="248.3814" x2="248.3814" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="392.1959" x2="392.1959" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="536.0103" x2="536.0103" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="679.8248" x2="679.8248" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="823.6392" x2="823.6392" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="967.4536" x2="967.4536" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="1111.2681" x2="1111.2681" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="1255.0825" x2="1255.0825" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="1398.8969" x2="1398.8969" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
      /><line x1="1542.7113" x2="1542.7113" y1="90" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="103.9506"
    /></g
    ><g transform="translate(248.3814,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-9.5" xml:space="preserve" y="35" style="stroke:none;"
      >1</text
    ></g
    ><g transform="translate(392.1959,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-9.5" xml:space="preserve" y="35" style="stroke:none;"
      >3</text
    ></g
    ><g transform="translate(536.0103,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-9.5" xml:space="preserve" y="35" style="stroke:none;"
      >5</text
    ></g
    ><g transform="translate(679.8248,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-9.5" xml:space="preserve" y="35" style="stroke:none;"
      >7</text
    ></g
    ><g transform="translate(823.6392,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-9.5" xml:space="preserve" y="35" style="stroke:none;"
      >9</text
    ></g
    ><g transform="translate(967.4536,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-19" xml:space="preserve" y="35" style="stroke:none;"
      >11</text
    ></g
    ><g transform="translate(1111.2681,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-19" xml:space="preserve" y="35" style="stroke:none;"
      >13</text
    ></g
    ><g transform="translate(1255.0825,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-19" xml:space="preserve" y="35" style="stroke:none;"
      >15</text
    ></g
    ><g transform="translate(1398.8969,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-19" xml:space="preserve" y="35" style="stroke:none;"
      >17</text
    ></g
    ><g transform="translate(1542.7113,1080.8003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-19" xml:space="preserve" y="35" style="stroke:none;"
      >19</text
    ></g
    ><g transform="translate(931.5007,1126.4668)" style="font-size:41.0667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-94.5" xml:space="preserve" y="38" style="stroke:none;"
      >Time (min)</text
    ></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><line y2="90" style="fill:none;" x1="234" x2="234" y1="1068"
      /><line y2="90" style="fill:none;" x1="1629" x2="1629" y1="1068"
      /><line y2="1068" style="fill:none;" x1="234" x2="247.95" y1="1068"
      /><line y2="1002.7997" style="fill:none;" x1="234" x2="247.95" y1="1002.7997"
      /><line y2="937.5995" style="fill:none;" x1="234" x2="247.95" y1="937.5995"
      /><line y2="872.4005" style="fill:none;" x1="234" x2="247.95" y1="872.4005"
      /><line y2="807.2003" style="fill:none;" x1="234" x2="247.95" y1="807.2003"
      /><line y2="742" style="fill:none;" x1="234" x2="247.95" y1="742"
      /><line y2="676.7997" style="fill:none;" x1="234" x2="247.95" y1="676.7997"
      /><line y2="611.5995" style="fill:none;" x1="234" x2="247.95" y1="611.5995"
      /><line y2="546.4005" style="fill:none;" x1="234" x2="247.95" y1="546.4005"
      /><line y2="481.2003" style="fill:none;" x1="234" x2="247.95" y1="481.2003"
      /><line y2="416" style="fill:none;" x1="234" x2="247.95" y1="416"
      /><line y2="350.7997" style="fill:none;" x1="234" x2="247.95" y1="350.7997"
      /><line y2="285.5995" style="fill:none;" x1="234" x2="247.95" y1="285.5995"
      /><line y2="220.4005" style="fill:none;" x1="234" x2="247.95" y1="220.4005"
      /><line y2="155.2003" style="fill:none;" x1="234" x2="247.95" y1="155.2003"
      /><line y2="90" style="fill:none;" x1="234" x2="247.95" y1="90"
      /><line y2="1068" style="fill:none;" x1="1629" x2="1615.05" y1="1068"
      /><line y2="1002.7997" style="fill:none;" x1="1629" x2="1615.05" y1="1002.7997"
      /><line y2="937.5995" style="fill:none;" x1="1629" x2="1615.05" y1="937.5995"
      /><line y2="872.4005" style="fill:none;" x1="1629" x2="1615.05" y1="872.4005"
      /><line y2="807.2003" style="fill:none;" x1="1629" x2="1615.05" y1="807.2003"
      /><line y2="742" style="fill:none;" x1="1629" x2="1615.05" y1="742"
      /><line y2="676.7997" style="fill:none;" x1="1629" x2="1615.05" y1="676.7997"
      /><line y2="611.5995" style="fill:none;" x1="1629" x2="1615.05" y1="611.5995"
      /><line y2="546.4005" style="fill:none;" x1="1629" x2="1615.05" y1="546.4005"
      /><line y2="481.2003" style="fill:none;" x1="1629" x2="1615.05" y1="481.2003"
      /><line y2="416" style="fill:none;" x1="1629" x2="1615.05" y1="416"
      /><line y2="350.7997" style="fill:none;" x1="1629" x2="1615.05" y1="350.7997"
      /><line y2="285.5995" style="fill:none;" x1="1629" x2="1615.05" y1="285.5995"
      /><line y2="220.4005" style="fill:none;" x1="1629" x2="1615.05" y1="220.4005"
      /><line y2="155.2003" style="fill:none;" x1="1629" x2="1615.05" y1="155.2003"
      /><line y2="90" style="fill:none;" x1="1629" x2="1615.05" y1="90"
    /></g
    ><g transform="translate(221.2,1068)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-38" xml:space="preserve" y="13.5" style="stroke:none;"
      >16</text
    ></g
    ><g transform="translate(221.2,1002.7997)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.1</text
    ></g
    ><g transform="translate(221.2,937.5995)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.2</text
    ></g
    ><g transform="translate(221.2,872.4005)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.3</text
    ></g
    ><g transform="translate(221.2,807.2003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.4</text
    ></g
    ><g transform="translate(221.2,742)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.5</text
    ></g
    ><g transform="translate(221.2,676.7997)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.6</text
    ></g
    ><g transform="translate(221.2,611.5995)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.7</text
    ></g
    ><g transform="translate(221.2,546.4005)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.8</text
    ></g
    ><g transform="translate(221.2,481.2003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >16.9</text
    ></g
    ><g transform="translate(221.2,416)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-38" xml:space="preserve" y="13.5" style="stroke:none;"
      >17</text
    ></g
    ><g transform="translate(221.2,350.7997)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >17.1</text
    ></g
    ><g transform="translate(221.2,285.5995)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >17.2</text
    ></g
    ><g transform="translate(221.2,220.4005)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >17.3</text
    ></g
    ><g transform="translate(221.2,155.2003)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >17.4</text
    ></g
    ><g transform="translate(221.2,90)" style="font-size:37.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66" xml:space="preserve" y="13.5" style="stroke:none;"
      >17.5</text
    ></g
    ><g transform="translate(147.2,579) rotate(-90)" style="font-size:41.0667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-83" xml:space="preserve" y="-9" style="stroke:none;"
      >SNR (dB)</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><path d="M248.3814 560.7441 L320.2887 534.0118 L392.1959 481.8519 L464.1031 522.2761 L536.0103 528.7962 L607.9175 489.0237 L679.8248 509.8874 L751.7319 500.1078 L823.6392 521.6245 L895.5464 526.1884 L967.4536 542.4882 L1039.3608 475.9834 L1111.2681 601.1683 L1183.1753 588.7796 L1255.0825 523.5806 L1326.9897 484.4597 L1398.8969 457.7275 L1470.8041 588.1279 L1542.7113 560.7441 L1614.6185 425.7796" style="fill:none; fill-rule:evenodd;"
      /><circle transform="translate(248.3814,560.7441)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,534.0118)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,481.8519)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,522.2761)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,528.7962)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,489.0237)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,509.8874)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,500.1078)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,521.6245)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,526.1884)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,542.4882)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,475.9834)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,601.1683)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,588.7796)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,523.5806)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,484.4597)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,457.7275)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,588.1279)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,560.7441)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,425.7796)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><path d="M248.3814 405.5675 L320.2887 367.0995 L392.1959 408.8282 L464.1031 303.8554 L536.0103 342.3234 L607.9175 447.9478 L679.8248 344.9325 L751.7319 353.4076 L823.6392 447.2962 L895.5464 411.436 L967.4536 442.7322 L1039.3608 372.968 L1111.2681 358.6244 L1183.1753 398.3957 L1255.0825 409.4799 L1326.9897 374.2725 L1398.8969 395.7879 L1470.8041 391.2239 L1542.7113 369.0557 L1614.6185 439.4715" style="fill:none; fill-rule:evenodd; stroke:rgb(217,83,25);"
      /><circle transform="translate(248.3814,405.5675)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,367.0995)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,408.8282)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,303.8554)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,342.3234)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,447.9478)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,344.9325)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,353.4076)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,447.2962)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,411.436)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,442.7322)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,372.968)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,358.6244)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,398.3957)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,409.4799)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,374.2725)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,395.7879)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,391.2239)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,369.0557)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,439.4715)" style="fill:none; stroke-linejoin:miter; stroke:rgb(217,83,25);" r="4" cx="0" cy="0"
      /><path d="M248.3814 208.6635 L320.2887 200.1884 L392.1959 161.0675 L464.1031 134.9882 L536.0103 140.2038 L607.9175 161.0675 L679.8248 216.4882 L751.7319 152.5924 L823.6392 174.7595 L895.5464 159.1126 L967.4536 131.0758 L1039.3608 103.0403 L1111.2681 157.8081 L1183.1753 172.8045 L1255.0825 196.9277 L1326.9897 190.4076 L1398.8969 171.6306 L1470.8041 236.2439 L1542.7113 149.9834 L1614.6185 152.5924" style="fill:none; fill-rule:evenodd; stroke:rgb(237,177,32);"
      /><circle transform="translate(248.3814,208.6635)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,200.1884)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,161.0675)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,134.9882)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,140.2038)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,161.0675)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,216.4882)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,152.5924)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,174.7595)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,159.1126)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,131.0758)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,103.0403)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,157.8081)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,172.8045)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,196.9277)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,190.4076)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,171.6306)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,236.2439)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,149.9834)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,152.5924)" style="fill:none; stroke-linejoin:miter; stroke:rgb(237,177,32);" r="4" cx="0" cy="0"
      /><path d="M248.3814 845.0166 L320.2887 842.4076 L392.1959 943.468 L464.1031 902.3922 L536.0103 848.9277 L607.9175 860.6635 L679.8248 827.4124 L751.7319 839.7997 L823.6392 860.0118 L895.5464 826.7595 L967.4536 886.7441 L1039.3608 784.3804 L1111.2681 861.3163 L1183.1753 879.5723 L1255.0825 853.4917 L1326.9897 875.0083 L1398.8969 882.8317 L1470.8041 813.0675 L1542.7113 843.0604 L1614.6185 953.2476" style="fill:none; fill-rule:evenodd; stroke:rgb(126,47,142);"
      /><circle transform="translate(248.3814,845.0166)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,842.4076)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,943.468)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,902.3922)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,848.9277)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,860.6635)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,827.4124)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,839.7997)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,860.0118)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,826.7595)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,886.7441)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,784.3804)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,861.3163)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,879.5723)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,853.4917)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,875.0083)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,882.8317)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,813.0675)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,843.0604)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,953.2476)" style="fill:none; stroke-linejoin:miter; stroke:rgb(126,47,142);" r="4" cx="0" cy="0"
      /><path d="M248.3814 339.7156 L320.2887 290.8164 L392.1959 225.6161 L464.1031 240.6126 L536.0103 265.3874 L607.9175 239.9597 L679.8248 286.904 L751.7319 248.436 L823.6392 230.1801 L895.5464 281.0355 L967.4536 205.404 L1039.3608 204.7524 L1111.2681 223.66 L1183.1753 253.6516 L1255.0825 208.0118 L1326.9897 290.1635 L1398.8969 244.5237 L1470.8041 202.7962 L1542.7113 215.8365 L1614.6185 227.5723" style="fill:none; fill-rule:evenodd; stroke:rgb(119,172,48);"
      /><circle transform="translate(248.3814,339.7156)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,290.8164)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,225.6161)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,240.6126)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,265.3874)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,239.9597)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,286.904)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,248.436)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,230.1801)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,281.0355)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,205.404)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,204.7524)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,223.66)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,253.6516)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,208.0118)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,290.1635)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,244.5237)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,202.7962)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,215.8365)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,227.5723)" style="fill:none; stroke-linejoin:miter; stroke:rgb(119,172,48);" r="4" cx="0" cy="0"
      /><path d="M248.3814 275.8199 L320.2887 267.9965 L392.1959 172.1516 L464.1031 198.8839 L536.0103 231.4834 L607.9175 264.7358 L679.8248 176.064 L751.7319 187.1481 L823.6392 154.5486 L895.5464 183.8874 L967.4536 159.7642 L1039.3608 197.5806 L1111.2681 182.5841 L1183.1753 333.8484 L1255.0825 261.4763 L1326.9897 249.0877 L1398.8969 249.7405 L1470.8041 219.096 L1542.7113 312.9834 L1614.6185 280.3839" style="fill:none; fill-rule:evenodd; stroke:rgb(77,190,238);"
      /><circle transform="translate(248.3814,275.8199)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,267.9965)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,172.1516)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,198.8839)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,231.4834)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,264.7358)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,176.064)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,187.1481)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,154.5486)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,183.8874)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,159.7642)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,197.5806)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,182.5841)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,333.8484)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,261.4763)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,249.0877)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,249.7405)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,219.096)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,312.9834)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,280.3839)" style="fill:none; stroke-linejoin:miter; stroke:rgb(77,190,238);" r="4" cx="0" cy="0"
      /><path d="M248.3814 269.2997 L320.2887 282.3401 L392.1959 335.1516 L464.1031 329.2844 L536.0103 376.8803 L607.9175 465.5522 L679.8248 453.1635 L751.7319 364.4917 L823.6392 356.0166 L895.5464 336.4562 L967.4536 415.3484 L1039.3608 410.7844 L1111.2681 365.7962 L1183.1753 445.9917 L1255.0825 401.6564 L1326.9897 372.3164 L1398.8969 386.6599 L1470.8041 382.7476 L1542.7113 417.9562 L1614.6185 411.436" style="fill:none; fill-rule:evenodd; stroke:rgb(162,20,47);"
      /><circle transform="translate(248.3814,269.2997)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(320.2887,282.3401)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(392.1959,335.1516)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(464.1031,329.2844)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(536.0103,376.8803)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(607.9175,465.5522)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(679.8248,453.1635)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(751.7319,364.4917)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(823.6392,356.0166)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(895.5464,336.4562)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(967.4536,415.3484)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1039.3608,410.7844)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1111.2681,365.7962)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1183.1753,445.9917)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1255.0825,401.6564)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1326.9897,372.3164)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1398.8969,386.6599)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1470.8041,382.7476)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1542.7113,417.9562)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
      /><circle transform="translate(1614.6185,411.436)" style="fill:none; stroke-linejoin:miter; stroke:rgb(162,20,47);" r="4" cx="0" cy="0"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><path style="stroke:none;" d="M1628 1056 L1628 1009 L271 1009 L271 1056 Z"
    /></g
    ><g transform="translate(365,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 1</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="279" x2="359" y1="1032"
      /><circle transform="translate(319,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g transform="translate(558,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 2</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="472" x2="552" y1="1032"
      /><circle transform="translate(512,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g transform="translate(751,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 3</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="665" x2="745" y1="1032"
      /><circle transform="translate(705,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g transform="translate(944,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 4</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="858" x2="938" y1="1032"
      /><circle transform="translate(898,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g transform="translate(1137,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 5</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(119,172,48); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(119,172,48); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="1051" x2="1131" y1="1032"
      /><circle transform="translate(1091,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g transform="translate(1330,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 6</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(77,190,238); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(77,190,238); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="1244" x2="1324" y1="1032"
      /><circle transform="translate(1284,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g transform="translate(1523,1032)" style="font-size:33.6px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="12" style="stroke:none;"
      >Fiber 7</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(162,20,47); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(162,20,47); color-interpolation:linearRGB; stroke-width:16;"
    ><line y2="1032" style="fill:none;" x1="1437" x2="1517" y1="1032"
      /><circle transform="translate(1477,1032)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g style="stroke-linecap:butt; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><path d="M271 1056 L271 1009 L1628 1009 L1628 1056 Z" style="fill:none; fill-rule:evenodd;"
    /></g
  ></g
></svg
>
