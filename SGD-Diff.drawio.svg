<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="506px" height="230px" viewBox="-0.5 -0.5 506 230" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.6 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36&quot; version=&quot;28.0.6&quot; pages=&quot;4&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;SGD Hardware Implementation&quot; id=&quot;SGD_hw&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;788&quot; dy=&quot;952&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;800&quot; pageHeight=&quot;400&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-25&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;init&quot; target=&quot;wait_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;194&quot; y=&quot;-110&quot; /&gt;&#10;              &lt;mxPoint x=&quot;194&quot; y=&quot;-18&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;init&quot; value=&quot;Initialize&amp;lt;br&amp;gt;Va₁,₀=Vc₁,₀=Va₂,n=Vc₂,n=0V&amp;lt;br&amp;gt;n=0&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;189&quot; y=&quot;-170&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-22&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;wait_measure&quot; target=&quot;va1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wait_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;214&quot; y=&quot;-28&quot; width=&quot;90&quot; height=&quot;38&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;&quot; parent=&quot;1&quot; source=&quot;va1_boundary&quot; target=&quot;va1_reset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;&quot; parent=&quot;1&quot; source=&quot;va1_boundary&quot; target=&quot;va1_perturb&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_boundary&quot; value=&quot;Va₁,n at&amp;#xa;boundary?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;224&quot; y=&quot;-94&quot; width=&quot;70&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_reset&quot; value=&quot;Va₁,n+1=0V&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-105&quot; width=&quot;70&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_perturb&quot; value=&quot;Va₁,n+1=Va₁,n+ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-61&quot; width=&quot;90&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_measure&quot; target=&quot;va1_evaluate&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n+1&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;462&quot; y=&quot;-92&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_evaluate&quot; target=&quot;vc1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;503&quot; y=&quot;20&quot; /&gt;&#10;              &lt;mxPoint x=&quot;259&quot; y=&quot;20&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_evaluate&quot; target=&quot;va1_correct&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_evaluate&quot; value=&quot;Iy,n &amp;amp;gt; Iy,n+1?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;463&quot; y=&quot;-28&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_correct&quot; target=&quot;vc1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;385&quot; y=&quot;20&quot; /&gt;&#10;              &lt;mxPoint x=&quot;259&quot; y=&quot;20&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_correct&quot; value=&quot;Va₁,n+1=Va₁,n-2ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-23&quot; width=&quot;100&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_boundary&quot; value=&quot;Vc₁,n at&amp;#xa;boundary?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;224&quot; y=&quot;48&quot; width=&quot;70&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_reset&quot; value=&quot;Vc₁,n+1=0V&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;39&quot; width=&quot;70&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_perturb&quot; value=&quot;Vc₁,n+1=Vc₁,n+ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;79&quot; width=&quot;90&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vc1_measure&quot; target=&quot;vc1_evaluate&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n+1&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;463&quot; y=&quot;53&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-20&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vc1_evaluate&quot; target=&quot;wait_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_evaluate&quot; value=&quot;Iy,n &amp;amp;gt; Iy,n+1?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;109&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_correct&quot; value=&quot;Vc₁,n+1=Vc₁,n-2ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;114&quot; width=&quot;100&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;aux_continuous&quot; value=&quot;Continuous Update&amp;#xa;Va₂,n+1=f(Va₁,n+1)&amp;#xa;Vc₂,n+1=f(Vc₁,n+1)&amp;#xa;(Boundary Mapping)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;342&quot; y=&quot;-170&quot; width=&quot;100&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;mapping_note&quot; value=&quot;f(x): Max→4V,&amp;amp;nbsp;&amp;lt;div&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; Min→-4V,&amp;amp;nbsp;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; else→0V&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#999999;fontStyle=2;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;452&quot; y=&quot;-170&quot; width=&quot;90&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;va1_reset&quot; target=&quot;va1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-92&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-70&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;va1_perturb&quot; target=&quot;va1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-48&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-70&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow11&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_boundary&quot; target=&quot;vc1_reset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow12&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_boundary&quot; target=&quot;vc1_perturb&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow13&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_reset&quot; target=&quot;vc1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;52&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;73&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_perturb&quot; target=&quot;vc1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;92&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;73&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow16&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_evaluate&quot; target=&quot;vc1_correct&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label1&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;-95&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label2&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;-55&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label3&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;443&quot; y=&quot;-24&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label4&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;47&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label5&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;89&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label6&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;442&quot; y=&quot;114&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-3&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;508&quot; y=&quot;151&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-4&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;504&quot; y=&quot;12&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;ol4MpNASkQ_DBQMsvc1G&quot; name=&quot;Diff&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;946&quot; dy=&quot;696&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-3&quot; target=&quot;Vad89lu7M3FoXwd2GboU-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-3&quot; target=&quot;Vad89lu7M3FoXwd2GboU-7&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-3&quot; value=&quot;ADC 接口 (ADC Interface)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;243&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-4&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-5&quot; target=&quot;Vad89lu7M3FoXwd2GboU-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-5&quot; value=&quot;累加模块 1 (Accumulator 1)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;210&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-7&quot; target=&quot;Vad89lu7M3FoXwd2GboU-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-7&quot; value=&quot;累加模块 2 (Accumulator 2)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;275&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-8&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-9&quot; target=&quot;Vad89lu7M3FoXwd2GboU-11&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-9&quot; value=&quot;差分减法器 (Subtractor)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;554&quot; y=&quot;243&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-10&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-11&quot; target=&quot;Vad89lu7M3FoXwd2GboU-13&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-11&quot; value=&quot;SGD 控制器&amp;amp;nbsp;&amp;lt;div&amp;gt;(SGD Controller)&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;554&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-12&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-13&quot; target=&quot;Vad89lu7M3FoXwd2GboU-20&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-13&quot; value=&quot;DAC 接口&amp;amp;nbsp;&amp;lt;div&amp;gt;(DAC Interface)&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-14&quot; value=&quot;ADC1 In&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;200&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-15&quot; value=&quot;ADC2 In&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;300&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-16&quot; value=&quot;Iy1&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;200&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-17&quot; value=&quot;Iy2&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;300&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-18&quot; value=&quot;Iy = Iy1 - Iy2&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;605&quot; y=&quot;310&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-19&quot; value=&quot;DAC Out&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;380&quot; y=&quot;400&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-20&quot; value=&quot;Control Voltages (Va1,Vc1,Va2,Vc2)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;N12PNBKsZnTtMPG24M_2&quot; name=&quot;框图&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1720&quot; dy=&quot;1204&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=12 12;strokeWidth=6;fillColor=#F5F5F5;strokeColor=#CCCCCC;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;477&quot; y=&quot;587&quot; width=&quot;1402&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;796&quot; y=&quot;1009&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;697&quot; y=&quot;945&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-4&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-5&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-7&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-5&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot;&amp;gt;DP-&amp;lt;/font&amp;gt;IQ&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;892&quot; y=&quot;979&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-6&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=3;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1155&quot; y=&quot;1009&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1300&quot; y=&quot;1010.12&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;EDFA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;triangle;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1074&quot; y=&quot;961.5&quot; width=&quot;84&quot; height=&quot;95&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1088&quot; y=&quot;945&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-9&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-10&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-22&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1428&quot; y=&quot;1061&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-10&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;&amp;quot;&amp;gt;ICR&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1301&quot; y=&quot;952&quot; width=&quot;85&quot; height=&quot;217&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-11&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-12&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-14&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-12&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;background-color: rgb(213, 232, 212);&amp;quot;&amp;gt;Laser2&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;676&quot; y=&quot;1091&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-13&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;entryX=-0.006;entryY=0.783;entryDx=0;entryDy=0;entryPerimeter=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-14&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-10&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1294&quot; y=&quot;1121&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1028&quot; y=&quot;1091&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-15&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-16&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-17&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-16&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1195&quot; y=&quot;979&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-17&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1208&quot; y=&quot;979&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-18&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1221&quot; y=&quot;979&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-19&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;858&quot; y=&quot;1091&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-20&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;872&quot; y=&quot;1091&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-21&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;888&quot; y=&quot;1091&quot; width=&quot;28&quot; height=&quot;28&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-22&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DSO&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1456&quot; y=&quot;1027.75&quot; width=&quot;116&quot; height=&quot;65.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-23&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;240GSa/s&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;AWG&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;879.5&quot; y=&quot;858&quot; width=&quot;145&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-24&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;913.38&quot; y=&quot;921&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;913.38&quot; y=&quot;978&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-25&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;938.38&quot; y=&quot;922&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;938.38&quot; y=&quot;979&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-26&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;965.38&quot; y=&quot;921&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;965.38&quot; y=&quot;978&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-27&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;989.38&quot; y=&quot;922&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;989.38&quot; y=&quot;979&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-28&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;696.5&quot; y=&quot;1062&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-29&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;strokeColor=#7EA6E0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;879&quot; y=&quot;891&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;663&quot; y=&quot;738&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;791&quot; y=&quot;816&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-30&quot; value=&quot;16QAM Mapping&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;684&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-31&quot; value=&quot;Frame Structure Construction&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;720&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-32&quot; value=&quot;Pre-Equalization&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;756&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-33&quot; value=&quot;Resampling&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;792&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-34&quot; value=&quot;Polarization Calibration&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;885&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-35&quot; value=&quot;CD Compensation&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;921&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-36&quot; value=&quot;Resampling&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;957&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-37&quot; value=&quot;DD-LMS&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;1029&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-38&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;strokeColor=#7EA6E0;fontSize=12;fontColor=#143642;fillColor=#FAE5C7;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1512&quot; y=&quot;1027&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1712.5000000000005&quot; y=&quot;849&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1586&quot; y=&quot;782&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-39&quot; value=&quot;Frequency Offset Estimation&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;993&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-40&quot; value=&quot;SNR&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;1065&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-41&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;background-color: rgb(213, 232, 212);&amp;quot;&amp;gt;Laser1&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;676&quot; y=&quot;979&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-42&quot; value=&quot;TX-DSP&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#7EA6E0;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;648&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-43&quot; value=&quot;RX-DSP&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#7EA6E0;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;849&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;2oF9lVC0x9H_EU1nuCiO&quot; name=&quot;APC&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1720&quot; dy=&quot;1204&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#E6E6E6;fillColor=#F5F5F5;fontColor=#333333;dashed=1;dashPattern=12 12;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;442&quot; y=&quot;420&quot; width=&quot;1254&quot; height=&quot;443&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-4&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-3&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontSize=12;fontColor=#143642;startSize=8;endSize=8;fillColor=#FAE5C7;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-4&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-4&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;733&quot; y=&quot;671&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-5&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1106&quot; y=&quot;699.6969696969697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-6&quot; value=&quot;&amp;lt;p style=&amp;quot;margin-top: 0pt; margin-bottom: 0pt; direction: ltr; unicode-bidi: embed; vertical-align: baseline;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;font-family: 微软雅黑;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;铌酸锂控制器&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/p&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;arcSize=15;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;908&quot; y=&quot;669&quot; width=&quot;122&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;FPGA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;arcSize=26;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1047&quot; y=&quot;454&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-8&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1235&quot; y=&quot;652&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-9&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1104&quot; y=&quot;684.25&quot; width=&quot;41&quot; height=&quot;29.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-10&quot; value=&quot;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;50:50&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;div&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;保偏耦合器&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;strokeColor=none;fillColor=none;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;labelBackgroundColor=none;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1087&quot; y=&quot;719&quot; width=&quot;87&quot; height=&quot;32&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-11&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-13&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1145&quot; y=&quot;703&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1214&quot; y=&quot;751&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-12&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#A8201A;fontSize=12;fontColor=#143642;startSize=8;endSize=8;fillColor=#FAE5C7;strokeWidth=3;dashed=1;dashPattern=8 8;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-13&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1523&quot; y=&quot;765&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-13&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1236&quot; y=&quot;736&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PBS&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1236&quot; y=&quot;623&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-15&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-17&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1327&quot; y=&quot;648&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1377&quot; y=&quot;598&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-16&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1327&quot; y=&quot;651.75&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1381&quot; y=&quot;697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-17&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1382&quot; y=&quot;573&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-18&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1382&quot; y=&quot;666&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1473&quot; y=&quot;601.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1546&quot; y=&quot;601.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-20&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1545&quot; y=&quot;601&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1165&quot; y=&quot;494&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1384&quot; y=&quot;494&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-21&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1473&quot; y=&quot;702&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1594&quot; y=&quot;702&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-22&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-7&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1595&quot; y=&quot;703&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1215&quot; y=&quot;596&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1545&quot; y=&quot;471&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-23&quot; value=&quot;&quot; style=&quot;edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1046&quot; y=&quot;487&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1096&quot; y=&quot;437&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;968&quot; y=&quot;487&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-24&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-25&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-4&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-25&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;Laser&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;523&quot; y=&quot;671&quot; width=&quot;109&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-26&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-9&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="Vad89lu7M3FoXwd2GboU-1"><g><path d="M 101 68 L 146 68 L 146 35 L 182.76 35" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 188.76 35 L 180.76 39 L 182.76 35 L 180.76 31 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-2"><g><path d="M 101 68 L 146 68 L 146 100 L 182.76 100" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 188.76 100 L 180.76 104 L 182.76 100 L 180.76 96 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-3"><g><rect x="1" y="43" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 68px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ADC 接口 (ADC Interface)</div></div></div></foreignObject><image x="2" y="54" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-4"><g><path d="M 291 35 L 333 35 L 333 68 L 366.76 68" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 372.76 68 L 364.76 72 L 366.76 68 L 364.76 64 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-5"><g><rect x="191" y="10" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 35px; margin-left: 192px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">累加模块 1 (Accumulator 1)</div></div></div></foreignObject><image x="192" y="21" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-6"><g><path d="M 291 100 L 333 100 L 333 68 L 366.76 68" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 372.76 68 L 364.76 72 L 366.76 68 L 364.76 64 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-7"><g><rect x="191" y="75" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 100px; margin-left: 192px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">累加模块 2 (Accumulator 2)</div></div></div></foreignObject><image x="192" y="86" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-8"><g><path d="M 425 93 L 425 141.76" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 425 147.76 L 421 139.76 L 425 141.76 L 429 139.76 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-9"><g><rect x="375" y="43" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 68px; margin-left: 376px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">差分减法器 (Subtractor)</div></div></div></foreignObject><image x="376" y="54" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-10"><g><path d="M 375 175 L 299.24 175" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 293.24 175 L 301.24 171 L 299.24 175 L 301.24 179 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-11"><g><rect x="375" y="150" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 175px; margin-left: 376px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">SGD 控制器 <div>(SGD Controller)</div></div></div></div></foreignObject><image x="376" y="161" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-12"><g><path d="M 191 175 L 109.24 175" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 103.24 175 L 111.24 171 L 109.24 175 L 111.24 179 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-13"><g><rect x="191" y="150" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 175px; margin-left: 192px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">DAC 接口 <div>(DAC Interface)</div></div></div></div></foreignObject><image x="192" y="161" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-14"><g><rect x="101" y="0" width="80" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 15px; margin-left: 102px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ADC1 In</div></div></div></foreignObject><image x="102" y="8.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-15"><g><rect x="101" y="100" width="80" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 115px; margin-left: 102px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ADC2 In</div></div></div></foreignObject><image x="102" y="108.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-16"><g><rect x="311" y="0" width="40" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 15px; margin-left: 312px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Iy1</div></div></div></foreignObject><image x="312" y="8.5" width="38" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-17"><g><rect x="311" y="100" width="40" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 115px; margin-left: 312px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Iy2</div></div></div></foreignObject><image x="312" y="108.5" width="38" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-18"><g><rect x="426" y="110" width="80" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 125px; margin-left: 427px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Iy = Iy1 - Iy2</div></div></div></foreignObject><image x="427" y="118.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-19"><g><rect x="201" y="200" width="80" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 215px; margin-left: 202px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">DAC Out</div></div></div></foreignObject><image x="202" y="208.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="Vad89lu7M3FoXwd2GboU-20"><g><rect x="1" y="150" width="100" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 175px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Control Voltages (Va1,Vc1,Va2,Vc2)</div></div></div></foreignObject><image x="2" y="161" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g></g></g></g></svg>