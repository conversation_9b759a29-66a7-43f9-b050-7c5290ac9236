<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.0//EN'
          'http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd'>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" style="fill-opacity:1; color-rendering:auto; color-interpolation:auto; text-rendering:auto; stroke:black; stroke-linecap:square; stroke-miterlimit:10; shape-rendering:auto; stroke-opacity:1; fill:black; stroke-dasharray:none; font-weight:normal; stroke-width:1; font-family:'Dialog'; font-style:normal; stroke-linejoin:miter; font-size:12px; stroke-dashoffset:0; image-rendering:auto;" width="1600" height="1200" xmlns="http://www.w3.org/2000/svg"
><!--Generated by the Batik Graphics2D SVG Generator--><defs id="genericDefs"
  /><g
  ><defs id="defs1"
    ><clipPath clipPathUnits="userSpaceOnUse" id="clipPath1"
      ><path d="M0 0 L1600 0 L1600 1200 L0 1200 L0 0 Z"
      /></clipPath
      ><font horiz-adv-x="75.0" id="font1"
      ><font-face ascent="100.53711" descent="21.972656" units-per-em="100" style="font-style:normal; font-family:Dialog; font-weight:normal;"
        /><missing-glyph horiz-adv-x="75.0" d="M12.5 0 L12.5 62.5 L62.5 62.5 L62.5 0 L12.5 0 ZM14.0625 1.5625 L60.9375 1.5625 L60.9375 60.9375 L14.0625 60.9375 L14.0625 1.5625 Z"
        /><glyph unicode="1" horiz-adv-x="55.615234" d="M37.25 0 L28.4688 0 L28.4688 56 Q25.2969 52.9844 20.1406 49.9531 Q14.9844 46.9219 10.8906 45.4062 L10.8906 53.9062 Q18.2656 57.375 23.7812 62.3047 Q29.2969 67.2344 31.5938 71.875 L37.25 71.875 L37.25 0 Z"
        /><glyph unicode="2" horiz-adv-x="55.615234" d="M50.3438 8.4531 L50.3438 0 L3.0312 0 Q2.9375 3.1719 4.0469 6.1094 Q5.8594 10.9375 9.8359 15.625 Q13.8125 20.3125 21.3438 26.4688 Q33.0156 36.0312 37.1172 41.625 Q41.2188 47.2188 41.2188 52.2031 Q41.2188 57.4219 37.4766 61.0078 Q33.7344 64.5938 27.7344 64.5938 Q21.3906 64.5938 17.5781 60.7891 Q13.7656 56.9844 13.7188 50.25 L4.6875 51.1719 Q5.6094 61.2812 11.6641 66.5781 Q17.7188 71.875 27.9375 71.875 Q38.2344 71.875 44.2422 66.1641 Q50.25 60.4531 50.25 52 Q50.25 47.7031 48.4922 43.5547 Q46.7344 39.4062 42.6562 34.8125 Q38.5781 30.2188 29.1094 22.2188 Q21.1875 15.5781 18.9453 13.2109 Q16.7031 10.8438 15.2344 8.4531 L50.3438 8.4531 Z"
        /><glyph unicode="3" horiz-adv-x="55.615234" d="M4.2031 18.8906 L12.9844 20.0625 Q14.5 12.5938 18.1406 9.2969 Q21.7812 6 27 6 Q33.2031 6 37.4766 10.2969 Q41.75 14.5938 41.75 20.9531 Q41.75 27 37.7969 30.9297 Q33.8438 34.8594 27.7344 34.8594 Q25.25 34.8594 21.5312 33.8906 L22.5156 41.6094 Q23.3906 41.5 23.9219 41.5 Q29.5469 41.5 34.0391 44.4297 Q38.5312 47.3594 38.5312 53.4688 Q38.5312 58.2969 35.2578 61.4766 Q31.9844 64.6562 26.8125 64.6562 Q21.6875 64.6562 18.2656 61.4297 Q14.8438 58.2031 13.875 51.7656 L5.0781 53.3281 Q6.6875 62.1562 12.3984 67.0156 Q18.1094 71.875 26.6094 71.875 Q32.4688 71.875 37.3984 69.3594 Q42.3281 66.8438 44.9453 62.5 Q47.5625 58.1562 47.5625 53.2656 Q47.5625 48.6406 45.0703 44.8281 Q42.5781 41.0156 37.7031 38.7656 Q44.0469 37.3125 47.5625 32.6953 Q51.0781 28.0781 51.0781 21.1406 Q51.0781 11.7656 44.2422 5.25 Q37.4062 -1.2656 26.9531 -1.2656 Q17.5312 -1.2656 11.3047 4.3516 Q5.0781 9.9688 4.2031 18.8906 Z"
        /><glyph unicode="4" horiz-adv-x="55.615234" d="M32.3281 0 L32.3281 17.1406 L1.2656 17.1406 L1.2656 25.2031 L33.9375 71.5781 L41.1094 71.5781 L41.1094 25.2031 L50.7812 25.2031 L50.7812 17.1406 L41.1094 17.1406 L41.1094 0 L32.3281 0 ZM32.3281 25.2031 L32.3281 57.4688 L9.9062 25.2031 L32.3281 25.2031 Z"
        /><glyph unicode="5" horiz-adv-x="55.615234" d="M4.1562 18.75 L13.375 19.5312 Q14.4062 12.7969 18.1406 9.3984 Q21.875 6 27.1562 6 Q33.5 6 37.8906 10.7891 Q42.2812 15.5781 42.2812 23.4844 Q42.2812 31 38.0625 35.3516 Q33.8438 39.7031 27 39.7031 Q22.75 39.7031 19.3359 37.7734 Q15.9219 35.8438 13.9688 32.7656 L5.7188 33.8438 L12.6406 70.6094 L48.25 70.6094 L48.25 62.2031 L19.6719 62.2031 L15.8281 42.9688 Q22.2656 47.4688 29.3438 47.4688 Q38.7188 47.4688 45.1641 40.9688 Q51.6094 34.4688 51.6094 24.2656 Q51.6094 14.5469 45.9531 7.4688 Q39.0625 -1.2188 27.1562 -1.2188 Q17.3906 -1.2188 11.2109 4.25 Q5.0312 9.7188 4.1562 18.75 Z"
        /><glyph unicode="6" horiz-adv-x="55.615234" d="M49.75 54.0469 L41.0156 53.375 Q39.8438 58.5469 37.7031 60.8906 Q34.125 64.6562 28.9062 64.6562 Q24.7031 64.6562 21.5312 62.3125 Q17.3906 59.2812 14.9922 53.4688 Q12.5938 47.6562 12.5 36.9219 Q15.6719 41.75 20.2656 44.0938 Q24.8594 46.4375 29.8906 46.4375 Q38.6719 46.4375 44.8516 39.9688 Q51.0312 33.5 51.0312 23.25 Q51.0312 16.5 48.125 10.7188 Q45.2188 4.9375 40.1406 1.8594 Q35.0625 -1.2188 28.6094 -1.2188 Q17.625 -1.2188 10.6953 6.8594 Q3.7656 14.9375 3.7656 33.5 Q3.7656 54.25 11.4219 63.6719 Q18.1094 71.875 29.4375 71.875 Q37.8906 71.875 43.2891 67.1406 Q48.6875 62.4062 49.75 54.0469 ZM13.875 23.1875 Q13.875 18.6562 15.7969 14.5078 Q17.7188 10.3594 21.1875 8.1797 Q24.6562 6 28.4688 6 Q34.0312 6 38.0391 10.4922 Q42.0469 14.9844 42.0469 22.7031 Q42.0469 30.125 38.0859 34.3984 Q34.125 38.6719 28.125 38.6719 Q22.1719 38.6719 18.0234 34.3984 Q13.875 30.125 13.875 23.1875 Z"
        /><glyph unicode="7" horiz-adv-x="55.615234" d="M4.7344 62.2031 L4.7344 70.6562 L51.0781 70.6562 L51.0781 63.8125 Q44.2344 56.5469 37.5234 44.4844 Q30.8125 32.4219 27.1562 19.6719 Q24.5156 10.6875 23.7812 0 L14.75 0 Q14.8906 8.4531 18.0625 20.4141 Q21.2344 32.375 27.1719 43.4844 Q33.1094 54.5938 39.7969 62.2031 L4.7344 62.2031 Z"
        /><glyph unicode="." horiz-adv-x="27.783203" d="M9.0781 0 L9.0781 10.0156 L19.0938 10.0156 L19.0938 0 L9.0781 0 Z"
        /><glyph unicode="8" horiz-adv-x="55.615234" d="M17.6719 38.8125 Q12.2031 40.8281 9.5703 44.5391 Q6.9375 48.25 6.9375 53.4219 Q6.9375 61.2344 12.5547 66.5547 Q18.1719 71.875 27.4844 71.875 Q36.8594 71.875 42.5781 66.4297 Q48.2969 60.9844 48.2969 53.1719 Q48.2969 48.1875 45.6797 44.5078 Q43.0625 40.8281 37.75 38.8125 Q44.3438 36.6719 47.7812 31.8828 Q51.2188 27.0938 51.2188 20.4531 Q51.2188 11.2812 44.7266 5.0312 Q38.2344 -1.2188 27.6406 -1.2188 Q17.0469 -1.2188 10.5469 5.0547 Q4.0469 11.3281 4.0469 20.7031 Q4.0469 27.6875 7.5938 32.3984 Q11.1406 37.1094 17.6719 38.8125 ZM15.9219 53.7188 Q15.9219 48.6406 19.1953 45.4141 Q22.4688 42.1875 27.6875 42.1875 Q32.7656 42.1875 36.0156 45.3828 Q39.2656 48.5781 39.2656 53.2188 Q39.2656 58.0625 35.9141 61.3594 Q32.5625 64.6562 27.5938 64.6562 Q22.5625 64.6562 19.2422 61.4297 Q15.9219 58.2031 15.9219 53.7188 ZM13.0938 20.6562 Q13.0938 16.8906 14.875 13.375 Q16.6562 9.8594 20.1719 7.9297 Q23.6875 6 27.7344 6 Q34.0312 6 38.1328 10.0547 Q42.2344 14.1094 42.2344 20.3594 Q42.2344 26.7031 38.0156 30.8594 Q33.7969 35.0156 27.4375 35.0156 Q21.2344 35.0156 17.1641 30.9141 Q13.0938 26.8125 13.0938 20.6562 Z"
      /></font
      ><font horiz-adv-x="77.7832" id="font2"
      ><font-face ascent="91.23535" descent="19.506836" units-per-em="100" style="font-style:normal; font-family:Times New Roman; font-weight:bold;"
        /><missing-glyph horiz-adv-x="77.7832" d="M13.875 0 L13.875 62.5 L63.875 62.5 L63.875 0 L13.875 0 ZM15.4375 1.5625 L62.3125 1.5625 L62.3125 60.9375 L15.4375 60.9375 L15.4375 1.5625 Z"
        /><glyph unicode=")" horiz-adv-x="33.30078" d="M1.2188 -20.0156 L1.2188 -17.7812 Q6.2969 -14.4062 8.6875 -11.2344 Q11.9688 -6.8438 13.875 0 Q16.2188 8.5938 16.2188 23.9219 Q16.2188 38.375 14.0938 46.3594 Q11.9688 54.3438 8.2969 59.2812 Q5.7656 62.7031 1.2188 65.2812 L1.2188 67.7188 Q12.9844 63.7656 21.1641 51.5859 Q29.3438 39.4062 29.3438 23.5781 Q29.3438 8.0156 21.1641 -4.0234 Q12.9844 -16.0625 1.2188 -20.0156 Z"
        /><glyph unicode="n" horiz-adv-x="55.615234" d="M22.0156 45.6562 L22.0156 39.75 Q25.5312 43.5625 28.7578 45.2891 Q31.9844 47.0156 35.6875 47.0156 Q40.1406 47.0156 43.0703 44.5547 Q46 42.0938 46.9688 38.4219 Q47.75 35.6406 47.75 27.7344 L47.75 9.9688 Q47.75 4.6875 48.7031 3.3906 Q49.6562 2.0938 52.6875 1.7656 L52.6875 0 L29.5938 0 L29.5938 1.7656 Q32.1719 2.0938 33.2969 3.9062 Q34.0781 5.125 34.0781 9.9688 L34.0781 30.2812 Q34.0781 35.8906 33.6406 37.3516 Q33.2031 38.8125 32.1562 39.625 Q31.1094 40.4375 29.8281 40.4375 Q25.6406 40.4375 22.0156 34.4219 L22.0156 9.9688 Q22.0156 4.8281 22.9688 3.4609 Q23.9219 2.0938 26.5156 1.7656 L26.5156 0 L3.4219 0 L3.4219 1.7656 Q6.2969 2.0469 7.5156 3.6094 Q8.3438 4.6875 8.3438 9.9688 L8.3438 35.75 Q8.3438 40.9219 7.3984 42.1875 Q6.4531 43.4531 3.4219 43.8438 L3.4219 45.6562 L22.0156 45.6562 Z"
        /><glyph unicode="(" horiz-adv-x="33.30078" d="M32.125 67.7188 L32.125 65.4844 Q27.0469 62.1562 24.6562 58.9844 Q21.3438 54.5938 19.4844 47.7031 Q17.1406 39.1562 17.1406 23.8281 Q17.1406 9.375 19.2656 1.3672 Q21.3906 -6.6406 25.0469 -11.5781 Q27.5938 -14.9844 32.125 -17.5781 L32.125 -20.0156 Q20.3594 -16.0625 12.1797 -3.8828 Q4 8.2969 4 24.125 Q4 39.75 12.1562 51.7812 Q20.3125 63.8125 32.125 67.7188 Z"
        /><glyph unicode=" " horiz-adv-x="25.0" d=""
        /><glyph unicode="e" horiz-adv-x="44.384766" d="M42.0469 24.4688 L17 24.4688 Q17.4375 15.375 21.8281 10.1094 Q25.2031 6.0625 29.9375 6.0625 Q32.8594 6.0625 35.25 7.6953 Q37.6406 9.3281 40.375 13.5781 L42.0469 12.5 Q38.3281 4.9375 33.8359 1.7812 Q29.3438 -1.375 23.4375 -1.375 Q13.2812 -1.375 8.0625 6.4531 Q3.8594 12.75 3.8594 22.0781 Q3.8594 33.5 10.0391 40.2578 Q16.2188 47.0156 24.5156 47.0156 Q31.4531 47.0156 36.5547 41.3281 Q41.6562 35.6406 42.0469 24.4688 ZM30.0312 27.7344 Q30.0312 35.5938 29.1797 38.5234 Q28.3281 41.4531 26.5156 42.9688 Q25.4844 43.8438 23.7812 43.8438 Q21.2344 43.8438 19.625 41.3594 Q16.75 37.0156 16.75 29.4375 L16.75 27.7344 L30.0312 27.7344 Z"
        /><glyph unicode="m" horiz-adv-x="83.30078" d="M22.3125 45.6562 L22.3125 39.6562 Q26.0781 43.75 29.2734 45.3828 Q32.4688 47.0156 36.2812 47.0156 Q40.6719 47.0156 43.6484 44.9688 Q46.625 42.9219 48.25 38.7188 Q52.1562 43.1094 55.7422 45.0625 Q59.3281 47.0156 63.2344 47.0156 Q67.9688 47.0156 70.7734 44.8438 Q73.5781 42.6719 74.6797 39.3281 Q75.7812 35.9844 75.7812 28.6562 L75.7812 9.9688 Q75.7812 4.6875 76.7344 3.4219 Q77.6875 2.1562 80.7188 1.7656 L80.7188 0 L57.125 0 L57.125 1.7656 Q59.9062 2 61.2344 3.9062 Q62.1094 5.2188 62.1094 9.9688 L62.1094 29.5938 Q62.1094 35.6875 61.625 37.3516 Q61.1406 39.0156 60.0859 39.8203 Q59.0312 40.625 57.625 40.625 Q55.5156 40.625 53.3672 39.0859 Q51.2188 37.5469 49.0312 34.4688 L49.0312 9.9688 Q49.0312 5.0312 49.8594 3.7656 Q50.9844 1.9531 54.1562 1.7656 L54.1562 0 L30.5156 0 L30.5156 1.7656 Q32.4219 1.8594 33.5234 2.7109 Q34.625 3.5625 34.9922 4.7578 Q35.3594 5.9531 35.3594 9.9688 L35.3594 29.5938 Q35.3594 35.7969 34.8672 37.3594 Q34.375 38.9219 33.2266 39.7969 Q32.0781 40.6719 30.7656 40.6719 Q28.8125 40.6719 27.2031 39.6562 Q24.9062 38.1406 22.3125 34.4688 L22.3125 9.9688 Q22.3125 5.125 23.2656 3.5859 Q24.2188 2.0469 27.25 1.7656 L27.25 0 L3.7188 0 L3.7188 1.7656 Q6.5938 2.0469 7.8125 3.6094 Q8.6406 4.6875 8.6406 9.9688 L8.6406 35.75 Q8.6406 40.9219 7.6875 42.1875 Q6.7344 43.4531 3.7188 43.8438 L3.7188 45.6562 L22.3125 45.6562 Z"
        /><glyph unicode="i" horiz-adv-x="27.783203" d="M14.3594 67.7812 Q17.5312 67.7812 19.7266 65.5547 Q21.9219 63.3281 21.9219 60.2031 Q21.9219 57.0781 19.7031 54.8828 Q17.4844 52.6875 14.3594 52.6875 Q11.2344 52.6875 9.0391 54.8828 Q6.8438 57.0781 6.8438 60.2031 Q6.8438 63.3281 9.0391 65.5547 Q11.2344 67.7812 14.3594 67.7812 ZM21.1875 45.6562 L21.1875 9.4688 Q21.1875 4.6406 22.3125 3.2969 Q23.4375 1.9531 26.7031 1.7656 L26.7031 0 L2.0469 0 L2.0469 1.7656 Q5.0781 1.8594 6.5469 3.5156 Q7.5156 4.6406 7.5156 9.4688 L7.5156 36.1406 Q7.5156 40.9688 6.3984 42.3125 Q5.2812 43.6562 2.0469 43.8438 L2.0469 45.6562 L21.1875 45.6562 Z"
        /><glyph unicode="T" horiz-adv-x="66.69922" d="M63.0938 66.2188 L63.0938 48.2969 L61.3281 48.2969 Q59.7656 54.5 57.8594 57.2031 Q55.9531 59.9062 52.6406 61.5312 Q50.7812 62.4062 46.1406 62.4062 L41.2188 62.4062 L41.2188 11.3281 Q41.2188 6.25 41.7734 4.9844 Q42.3281 3.7188 43.9688 2.7656 Q45.6094 1.8125 48.4375 1.8125 L50.6406 1.8125 L50.6406 0 L15.9688 0 L15.9688 1.8125 L18.1719 1.8125 Q21.0469 1.8125 22.7969 2.8281 Q24.0781 3.5156 24.8125 5.1719 Q25.3438 6.3438 25.3438 11.3281 L25.3438 62.4062 L20.5625 62.4062 Q13.875 62.4062 10.8438 59.5781 Q6.5938 55.6094 5.4688 48.2969 L3.6094 48.2969 L3.6094 66.2188 L63.0938 66.2188 Z"
        /><glyph unicode="B" horiz-adv-x="66.69922" d="M44.4844 35.1094 Q53.9062 32.9062 57.7188 29.7812 Q63.1875 25.3438 63.1875 18.2656 Q63.1875 10.7969 57.1719 5.9062 Q49.8125 0 35.75 0 L2.0938 0 L2.0938 1.8125 Q6.6875 1.8125 8.3281 2.6641 Q9.9688 3.5156 10.625 4.8828 Q11.2812 6.25 11.2812 11.625 L11.2812 54.5938 Q11.2812 59.9688 10.625 61.3594 Q9.9688 62.75 8.3047 63.5781 Q6.6406 64.4062 2.0938 64.4062 L2.0938 66.2188 L33.8438 66.2188 Q45.2188 66.2188 49.9531 64.1875 Q54.6875 62.1562 57.4219 58.1328 Q60.1562 54.1094 60.1562 49.5625 Q60.1562 44.7812 56.6875 41.0469 Q53.2188 37.3125 44.4844 35.1094 ZM26.7031 36.4219 Q33.6406 36.4219 36.9375 37.9844 Q40.2344 39.5469 41.9922 42.3828 Q43.75 45.2188 43.75 49.6094 Q43.75 54 42.0156 56.8125 Q40.2812 59.625 37.0625 61.0859 Q33.8438 62.5469 26.7031 62.5 L26.7031 36.4219 ZM26.7031 32.625 L26.7031 11.375 L26.6562 8.9375 Q26.6562 6.2969 28 4.9531 Q29.3438 3.6094 31.9844 3.6094 Q35.8906 3.6094 39.1875 5.3438 Q42.4844 7.0781 44.2422 10.375 Q46 13.6719 46 17.7188 Q46 22.3594 43.8516 26.0469 Q41.7031 29.7344 37.9453 31.2031 Q34.1875 32.6719 26.7031 32.625 Z"
        /><glyph unicode="d" horiz-adv-x="55.615234" d="M47.75 66.2188 L47.75 13.6719 Q47.75 8.3438 48.0469 7.375 Q48.4375 5.7656 49.5391 4.9844 Q50.6406 4.2031 53.375 4 L53.375 2.3906 L34.0781 -1.375 L34.0781 5.8594 Q30.6094 1.6562 27.9062 0.1406 Q25.2031 -1.375 21.7812 -1.375 Q13.0312 -1.375 7.9531 6.4531 Q3.8594 12.7969 3.8594 22.0156 Q3.8594 29.3906 6.3984 35.2266 Q8.9375 41.0625 13.3594 44.0391 Q17.7812 47.0156 22.8594 47.0156 Q26.125 47.0156 28.6641 45.75 Q31.2031 44.4844 34.0781 41.3125 L34.0781 55.0781 Q34.0781 60.2969 33.6406 61.375 Q33.0625 62.7969 31.8906 63.4766 Q30.7188 64.1562 27.4844 64.1562 L27.4844 66.2188 L47.75 66.2188 ZM34.0781 35.25 Q30.4688 42.1875 25.25 42.1875 Q23.4375 42.1875 22.2656 41.2188 Q20.4531 39.7031 19.3125 35.8906 Q18.1719 32.0781 18.1719 24.2188 Q18.1719 15.5781 19.4375 11.4297 Q20.7031 7.2812 22.9062 5.4219 Q24.0312 4.5 25.9844 4.5 Q30.2812 4.5 34.0781 11.2344 L34.0781 35.25 Z"
        /><glyph unicode="R" horiz-adv-x="72.2168" d="M26.5625 30.1719 L26.5625 11.625 Q26.5625 6.25 27.2188 4.8594 Q27.875 3.4688 29.5391 2.6406 Q31.2031 1.8125 35.75 1.8125 L35.75 0 L1.8594 0 L1.8594 1.8125 Q6.4531 1.8125 8.0859 2.6641 Q9.7188 3.5156 10.375 4.8828 Q11.0312 6.25 11.0312 11.625 L11.0312 54.5938 Q11.0312 59.9688 10.375 61.3594 Q9.7188 62.75 8.0547 63.5781 Q6.3906 64.4062 1.8594 64.4062 L1.8594 66.2188 L32.625 66.2188 Q44.625 66.2188 50.1953 64.5547 Q55.7656 62.8906 59.2812 58.4219 Q62.7969 53.9531 62.7969 47.9531 Q62.7969 40.625 57.5156 35.8438 Q54.1562 32.8125 48.0938 31.2969 L64.0156 8.8906 Q67.1406 4.5469 68.4531 3.4688 Q70.4531 1.9531 73.0938 1.8125 L73.0938 0 L52.25 0 L30.9062 30.1719 L26.5625 30.1719 ZM26.5625 62.6406 L26.5625 33.6406 L29.3438 33.6406 Q36.1406 33.6406 39.5078 34.8906 Q42.875 36.1406 44.8047 39.3828 Q46.7344 42.625 46.7344 47.8594 Q46.7344 55.4219 43.1953 59.0312 Q39.6562 62.6406 31.7812 62.6406 L26.5625 62.6406 Z"
        /><glyph unicode="N" horiz-adv-x="72.2168" d="M24.9062 66.2188 L57.5625 25.1406 L57.5625 53.6094 Q57.5625 59.5781 55.8594 61.6719 Q53.5156 64.5 48 64.4062 L48 66.2188 L69.875 66.2188 L69.875 64.4062 Q65.6719 63.875 64.2344 63.0156 Q62.7969 62.1562 61.9922 60.2266 Q61.1875 58.2969 61.1875 53.6094 L61.1875 -1.5156 L59.5156 -1.5156 L14.75 53.6094 L14.75 11.5312 Q14.75 5.8125 17.3594 3.8125 Q19.9688 1.8125 23.3438 1.8125 L24.9062 1.8125 L24.9062 0 L1.4219 0 L1.4219 1.8125 Q6.8906 1.8594 9.0391 4.0547 Q11.1875 6.25 11.1875 11.5312 L11.1875 58.25 L9.7656 60.0156 Q7.6719 62.6406 6.0547 63.4766 Q4.4375 64.3125 1.4219 64.4062 L1.4219 66.2188 L24.9062 66.2188 Z"
        /><glyph unicode="S" horiz-adv-x="55.615234" d="M46.9688 67.7188 L47.5156 45.6562 L45.5156 45.6562 Q44.0938 53.9531 38.5547 59.0078 Q33.0156 64.0625 26.5625 64.0625 Q21.5781 64.0625 18.6719 61.3984 Q15.7656 58.7344 15.7656 55.2812 Q15.7656 53.0781 16.7969 51.375 Q18.2188 49.0781 21.3438 46.8281 Q23.6406 45.2188 31.9375 41.1094 Q43.5625 35.4062 47.6094 30.3281 Q51.6094 25.25 51.6094 18.7031 Q51.6094 10.4062 45.1406 4.4219 Q38.6719 -1.5625 28.7188 -1.5625 Q25.5938 -1.5625 22.8047 -0.9297 Q20.0156 -0.2969 15.8281 1.4688 Q13.4844 2.4375 11.9688 2.4375 Q10.6875 2.4375 9.2734 1.4609 Q7.8594 0.4844 6.9844 -1.5156 L5.1719 -1.5156 L5.1719 23.4844 L6.9844 23.4844 Q9.125 12.9375 15.2578 7.3984 Q21.3906 1.8594 28.4688 1.8594 Q33.9375 1.8594 37.1875 4.8359 Q40.4375 7.8125 40.4375 11.7656 Q40.4375 14.1094 39.1875 16.3047 Q37.9375 18.5 35.3984 20.4844 Q32.8594 22.4688 26.4219 25.6406 Q17.3906 30.0781 13.4297 33.2031 Q9.4688 36.3281 7.3438 40.1875 Q5.2188 44.0469 5.2188 48.6875 Q5.2188 56.5938 11.0312 62.1562 Q16.8438 67.7188 25.6875 67.7188 Q28.9062 67.7188 31.9375 66.9375 Q34.2344 66.3594 37.5312 64.7734 Q40.8281 63.1875 42.1406 63.1875 Q43.4062 63.1875 44.1406 63.9688 Q44.875 64.75 45.5156 67.7188 L46.9688 67.7188 Z"
      /></font
    ></defs
    ><g style="fill:white; stroke:white;"
    ><rect x="0" y="0" width="1600" style="clip-path:url(#clipPath1); stroke:none;" height="1200"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><rect x="0" width="1600" height="1200" y="0" style="stroke:none;"
      /><path style="stroke:none;" d="M208 1068 L1448 1068 L1448 90 L208 90 Z"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="89.9992" style="fill:none;" x1="249.3333" x2="249.3333" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="290.6667" x2="290.6667" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="332" x2="332" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="373.3333" x2="373.3333" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="456" x2="456" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="497.3333" x2="497.3333" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="538.6666" x2="538.6666" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="580" x2="580" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="662.6667" x2="662.6667" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="704" x2="704" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="745.3333" x2="745.3333" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="786.6667" x2="786.6667" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="869.3333" x2="869.3333" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="910.6667" x2="910.6667" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="952" x2="952" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="993.3334" x2="993.3334" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1076" x2="1076" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1117.3334" x2="1117.3334" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1158.6666" x2="1158.6666" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1200" x2="1200" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1282.6666" x2="1282.6666" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1324" x2="1324" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1365.3334" x2="1365.3334" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1406.6667" x2="1406.6667" y1="1068.0009"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="89.9992" style="fill:none;" x1="208" x2="208" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="414.6667" x2="414.6667" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="621.3333" x2="621.3333" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="828" x2="828" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1034.6666" x2="1034.6666" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1241.3334" x2="1241.3334" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1448" x2="1448" y1="1068.0009"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="1046.267" style="fill:none;" x1="1448" x2="208" y1="1046.267"
      /><line y2="1024.5332" style="fill:none;" x1="1448" x2="208" y1="1024.5332"
      /><line y2="1002.7993" style="fill:none;" x1="1448" x2="208" y1="1002.7993"
      /><line y2="981.0655" style="fill:none;" x1="1448" x2="208" y1="981.0655"
      /><line y2="937.602" style="fill:none;" x1="1448" x2="208" y1="937.602"
      /><line y2="915.8682" style="fill:none;" x1="1448" x2="208" y1="915.8682"
      /><line y2="894.1343" style="fill:none;" x1="1448" x2="208" y1="894.1343"
      /><line y2="872.4005" style="fill:none;" x1="1448" x2="208" y1="872.4005"
      /><line y2="828.9329" style="fill:none;" x1="1448" x2="208" y1="828.9329"
      /><line y2="807.199" style="fill:none;" x1="1448" x2="208" y1="807.199"
      /><line y2="785.4651" style="fill:none;" x1="1448" x2="208" y1="785.4651"
      /><line y2="763.7313" style="fill:none;" x1="1448" x2="208" y1="763.7313"
      /><line y2="720.2678" style="fill:none;" x1="1448" x2="208" y1="720.2678"
      /><line y2="698.534" style="fill:none;" x1="1448" x2="208" y1="698.534"
      /><line y2="676.8002" style="fill:none;" x1="1448" x2="208" y1="676.8002"
      /><line y2="655.0663" style="fill:none;" x1="1448" x2="208" y1="655.0663"
      /><line y2="611.5987" style="fill:none;" x1="1448" x2="208" y1="611.5987"
      /><line y2="589.8649" style="fill:none;" x1="1448" x2="208" y1="589.8649"
      /><line y2="568.1351" style="fill:none;" x1="1448" x2="208" y1="568.1351"
      /><line y2="546.4013" style="fill:none;" x1="1448" x2="208" y1="546.4013"
      /><line y2="502.9337" style="fill:none;" x1="1448" x2="208" y1="502.9337"
      /><line y2="481.1998" style="fill:none;" x1="1448" x2="208" y1="481.1998"
      /><line y2="459.466" style="fill:none;" x1="1448" x2="208" y1="459.466"
      /><line y2="437.7322" style="fill:none;" x1="1448" x2="208" y1="437.7322"
      /><line y2="394.2686" style="fill:none;" x1="1448" x2="208" y1="394.2686"
      /><line y2="372.5348" style="fill:none;" x1="1448" x2="208" y1="372.5348"
      /><line y2="350.801" style="fill:none;" x1="1448" x2="208" y1="350.801"
      /><line y2="329.0672" style="fill:none;" x1="1448" x2="208" y1="329.0672"
      /><line y2="285.5995" style="fill:none;" x1="1448" x2="208" y1="285.5995"
      /><line y2="263.8657" style="fill:none;" x1="1448" x2="208" y1="263.8657"
      /><line y2="242.1318" style="fill:none;" x1="1448" x2="208" y1="242.1318"
      /><line y2="220.398" style="fill:none;" x1="1448" x2="208" y1="220.398"
      /><line y2="176.9345" style="fill:none;" x1="1448" x2="208" y1="176.9345"
      /><line y2="155.2007" style="fill:none;" x1="1448" x2="208" y1="155.2007"
      /><line y2="133.4668" style="fill:none;" x1="1448" x2="208" y1="133.4668"
      /><line y2="111.733" style="fill:none;" x1="1448" x2="208" y1="111.733"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="1068.0009" style="fill:none;" x1="1448" x2="208" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="1448" x2="208" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="1448" x2="208" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="1448" x2="208" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="1448" x2="208" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="1448" x2="208" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="1448" x2="208" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="1448" x2="208" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="1448" x2="208" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="1448" x2="208" y1="89.9992"
      /><line x1="208" x2="1448" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1068.0009"
      /><line x1="208" x2="1448" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="89.9992"
      /><line x1="208" x2="208" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="414.6667" x2="414.6667" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="621.3333" x2="621.3333" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="828" x2="828" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="1034.6666" x2="1034.6666" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="1241.3334" x2="1241.3334" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="1448" x2="1448" y1="1068.0009" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="1055.6022"
      /><line x1="208" x2="208" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
      /><line x1="414.6667" x2="414.6667" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
      /><line x1="621.3333" x2="621.3333" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
      /><line x1="828" x2="828" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
      /><line x1="1034.6666" x2="1034.6666" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
      /><line x1="1241.3334" x2="1241.3334" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
      /><line x1="1448" x2="1448" y1="89.9992" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="102.3978"
    /></g
    ><g transform="translate(208,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >1</text
    ></g
    ><g transform="translate(414.6667,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >2</text
    ></g
    ><g transform="translate(621.3333,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >3</text
    ></g
    ><g transform="translate(828,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >4</text
    ></g
    ><g transform="translate(1034.6666,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >5</text
    ></g
    ><g transform="translate(1241.3334,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >6</text
    ></g
    ><g transform="translate(1448,1078.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-7.5" xml:space="preserve" y="27" style="stroke:none;"
      >7</text
    ></g
    ><g transform="translate(828.0006,1114.3329)" style="font-size:53.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB; font-weight:bold;"
    ><text x="-128.5" xml:space="preserve" y="49" style="stroke:none;"
      >Time (min)</text
    ></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><line y2="89.9992" style="fill:none;" x1="208" x2="208" y1="1068.0009"
      /><line y2="89.9992" style="fill:none;" x1="1448" x2="1448" y1="1068.0009"
      /><line y2="1068.0009" style="fill:none;" x1="208" x2="220.4" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="208" x2="220.4" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="208" x2="220.4" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="208" x2="220.4" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="208" x2="220.4" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="208" x2="220.4" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="208" x2="220.4" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="208" x2="220.4" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="208" x2="220.4" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="208" x2="220.4" y1="89.9992"
      /><line y2="1068.0009" style="fill:none;" x1="1448" x2="1435.6" y1="1068.0009"
      /><line y2="959.3317" style="fill:none;" x1="1448" x2="1435.6" y1="959.3317"
      /><line y2="850.6667" style="fill:none;" x1="1448" x2="1435.6" y1="850.6667"
      /><line y2="742.0016" style="fill:none;" x1="1448" x2="1435.6" y1="742.0016"
      /><line y2="633.3325" style="fill:none;" x1="1448" x2="1435.6" y1="633.3325"
      /><line y2="524.6675" style="fill:none;" x1="1448" x2="1435.6" y1="524.6675"
      /><line y2="415.9984" style="fill:none;" x1="1448" x2="1435.6" y1="415.9984"
      /><line y2="307.3333" style="fill:none;" x1="1448" x2="1435.6" y1="307.3333"
      /><line y2="198.6683" style="fill:none;" x1="1448" x2="1435.6" y1="198.6683"
      /><line y2="89.9992" style="fill:none;" x1="1448" x2="1435.6" y1="89.9992"
    /></g
    ><g transform="translate(197.3333,1068.0009)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.4</text
    ></g
    ><g transform="translate(197.3333,959.3317)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.45</text
    ></g
    ><g transform="translate(197.3333,850.6667)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.5</text
    ></g
    ><g transform="translate(197.3333,742.0016)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.55</text
    ></g
    ><g transform="translate(197.3333,633.3325)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.6</text
    ></g
    ><g transform="translate(197.3333,524.6675)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.65</text
    ></g
    ><g transform="translate(197.3333,415.9984)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.7</text
    ></g
    ><g transform="translate(197.3333,307.3333)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.75</text
    ></g
    ><g transform="translate(197.3333,198.6683)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-52" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.8</text
    ></g
    ><g transform="translate(197.3333,89.9992)" style="font-size:26.6667px; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-67" xml:space="preserve" y="10.5" style="stroke:none;"
      >16.85</text
    ></g
    ><g transform="translate(122.3333,579) rotate(-90)" style="font-size:53.3333px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB; font-weight:bold;"
    ><text x="-110.5" xml:space="preserve" y="-11" style="stroke:none;"
      >SNR (dB)</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><path d="M208 183.6706 L414.6667 161.7212 L621.3333 179.1066 L828 257.3451 L1034.6666 236.7015 L1241.3334 263.8657 L1448 268.2141" style="fill:none; fill-rule:evenodd;"
    /></g
    ><g transform="translate(208,183.6706)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(208,183.6706)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,161.7212)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,161.7212)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,179.1066)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,179.1066)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,257.3451)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,257.3451)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,236.7015)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,236.7015)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,263.8657)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,263.8657)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,268.2141)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,268.2141)" style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1448,-268.2141)" style="fill:none; fill-rule:evenodd; stroke:rgb(217,83,25); stroke-linejoin:round;" d="M208 350.801 L414.6667 320.3745 L621.3333 448.6012 L828 494.241 L1034.6666 392.0924 L1241.3334 422.5189 L1448 370.3586"
    /></g
    ><g transform="translate(208,350.801)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(208,350.801)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,320.3745)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,320.3745)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,448.6012)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,448.6012)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,494.241)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,494.241)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,392.0924)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,392.0924)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,422.5189)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,422.5189)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,370.3586)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,370.3586)" style="stroke-linecap:butt; fill:rgb(217,83,25); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(217,83,25); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1448,-370.3586)" style="fill:none; fill-rule:evenodd; stroke:rgb(237,177,32); stroke-linejoin:round;" d="M208 511.6264 L414.6667 529.0118 L621.3333 546.4013 L828 692.0134 L1034.6666 539.8807 L1241.3334 518.1469 L1448 548.5735"
    /></g
    ><g transform="translate(208,511.6264)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(208,511.6264)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,529.0118)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,529.0118)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,546.4013)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,546.4013)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,692.0134)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,692.0134)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,539.8807)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,539.8807)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,518.1469)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,518.1469)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,548.5735)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,548.5735)" style="stroke-linecap:butt; fill:rgb(237,177,32); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(237,177,32); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
      /><path transform="translate(-1448,-548.5735)" style="fill:none; fill-rule:evenodd; stroke:rgb(126,47,142); stroke-linejoin:round;" d="M208 781.1209 L414.6667 715.9194 L621.3333 1018.0126 L828 881.0932 L1034.6666 692.0134 L1241.3334 628.9882 L1448 722.4399"
    /></g
    ><g transform="translate(208,781.1209)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(208,781.1209)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,715.9194)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(414.6667,715.9194)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,1018.0126)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(621.3333,1018.0126)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,881.0932)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(828,881.0932)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,692.0134)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1034.6666,692.0134)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,628.9882)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1241.3334,628.9882)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,722.4399)" style="fill:white; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; color-interpolation:linearRGB; stroke:white;"
    ><circle r="4" style="stroke:none;" cx="0" cy="0"
    /></g
    ><g transform="translate(1448,722.4399)" style="stroke-linecap:butt; fill:rgb(126,47,142); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(126,47,142); color-interpolation:linearRGB; stroke-width:16;"
    ><circle r="4" style="fill:none;" cx="0" cy="0"
    /></g
  ></g
></svg
>
