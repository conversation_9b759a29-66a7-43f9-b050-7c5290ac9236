clc;
clear all;
close all;

x = 1:20;

% 纵坐标数据
y = [16.391, 16.391, 16.413, 15.609, 16.199, 14.549, 10.195, 9.5442, ...
     11.78, 11.2997, 14.893, 15.273, 15.28, 15.379, 13.997, 15.549, ...
     14.796, 11.281, 15.03, 15.059];

figure;
set(gcf, 'Position', [100, 100, 900, 600]); % 设置图形大小

plot(x, y, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', '60G-16QAM');
hold on;

% grid on;
xlabel('Time(min)', 'FontSize', 20, 'Fontname', 'Times New Roman');
ylabel('SNR(dB)', 'FontSize', 20, 'Fontname', 'Times New Roman');
% title('NO-APC', 'FontSize', 16, 'Fontname', 'Times New Roman','color','k');
legend('show', 'Location', 'best');
set(gca, 'FontSize', 14, 'Fontname', 'Times New Roman');

% 设置正确的坐标轴范围
xlim([1, 20]);  % 横坐标范围 1-20
ylim([8, 17]);  % 纵坐标范围根据数据调整为8-17

% % 添加最低点标记
% [ymin, idx] = min(y);  % 找到最小值及其位置
% plot(x(idx), ymin, 'rs', 'MarkerSize', 10, 'LineWidth', 2, 'DisplayName', 'Min BER');
% text(x(idx)+0.5, ymin+0.3, sprintf('Min: %.2f', ymin), ...
%      'FontSize', 12, 'Fontname', 'Times New Roman', 'Color', 'r');
%% 

% 添加网格线增强可读性
% grid minor;

clc;
clear all;
% close all;

% 横坐标：时间 (1-20)
x = 1:20;

% 七条光纤的SNR数据 (单位：dB)，每条光纤有20个数据
fiber1 = [16.778, 16.819, 16.899, 16.837, 16.827, 16.888, 16.856, 16.871, 16.838, 16.831, 16.806, 16.908, 16.716, 16.735, 16.835, 16.895, 16.936, 16.736, 16.778, 16.985];
fiber2 = [17.016, 17.075, 17.011, 17.172, 17.113, 16.951, 17.109, 17.096, 16.952, 17.007, 16.959, 17.066, 17.088, 17.027, 17.01, 17.064, 17.031, 17.038, 17.072, 16.964];
fiber3 = [17.318, 17.331, 17.391, 17.431, 17.423, 17.391, 17.306, 17.404, 17.37, 17.394, 17.437, 17.48, 17.396, 17.373, 17.336, 17.346, 17.3748, 17.2757, 17.408, 17.404];
fiber4 = [16.342, 16.346, 16.191, 16.254, 16.336, 16.318, 16.369, 16.35, 16.319, 16.37, 16.278, 16.435, 16.317, 16.289, 16.329, 16.296, 16.284, 16.391, 16.345, 16.176];
fiber5 = [17.117, 17.192, 17.292, 17.269, 17.231, 17.27, 17.198, 17.257, 17.285, 17.207, 17.323, 17.324, 17.295, 17.249, 17.319, 17.193, 17.263, 17.327, 17.307, 17.289];
fiber6 = [17.215, 17.227, 17.374, 17.333, 17.283, 17.232, 17.368, 17.351, 17.401, 17.356, 17.393, 17.335, 17.358, 17.126, 17.237, 17.256, 17.255, 17.302, 17.158, 17.208];
fiber7 = [17.225, 17.205, 17.124, 17.133, 17.06, 16.924, 16.943, 17.079, 17.092, 17.122, 17.001, 17.008, 17.077, 16.954, 17.022, 17.067, 17.045, 17.051, 16.997, 17.007];

% 创建图形
figure;
set(gcf, 'Position', [100, 100, 900, 600]); % 设置图形大小

% 绘制七条光纤的曲线
plot(x, fiber1, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 1');
hold on;
plot(x, fiber2, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 2');
plot(x, fiber3, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 3');
plot(x, fiber4, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 4');
plot(x, fiber5, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 5');
plot(x, fiber6, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 6');
plot(x, fiber7, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 7');

% % 添加网格线
% grid on;
% grid minor;

% 设置坐标轴标签
xlabel('Time (min)', 'FontSize', 15, 'Fontname', 'Times New Roman');
ylabel('SNR (dB)', 'FontSize', 15, 'Fontname', 'Times New Roman'); % SNR单位为dB
% title('Seven Fiber SNR under APC Situation', 'FontSize', 16, 'Fontname', 'Times New Roman', 'color', 'k');

% 添加图例
legend('show', 'Location', 'best');

% 设置坐标轴范围
xlim([0.8, 20.2]);  % 横坐标稍微扩展
ylim([16, 17.5]); % 纵坐标范围根据数据设置

% 设置刻度
set(gca, 'FontSize', 14, 'Fontname', 'Times New Roman');
set(gca, 'XTick', 1:2:20); % 明确设置横坐标刻度
set(gca, 'YTick', 16:0.1:17.5); % 设置纵坐标刻度，每隔0.05增加

% 美化图形
box on; % 添加边框
%% 
clc;
clear all;
% close all;

% 横坐标：时间 (1-7分钟)
x = 1:7;

% 四条曲线的SNR数据 (单位：dB)
y1 = [16.8069, 16.817, 16.809, 16.773, 16.7825, 16.77, 16.768]; % 1k频率
y2 = [16.73, 16.744, 16.685, 16.664, 16.711, 16.697, 16.721];  % 10k频率
y3 = [16.656, 16.648, 16.64, 16.573, 16.643, 16.653, 16.639];  % 100k频率
y4 = [16.532, 16.562, 16.423, 16.486, 16.573, 16.602, 16.559]; % 1000k频率

% 创建图形并设置大尺寸
figure;
set(gcf, 'Position', [100, 100, 900, 600]); % 放大图形尺寸 (1200x900像素)

% 绘制四条曲线（加粗线条）
plot(x, y1, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '1 kHz');
hold on;
plot(x, y2, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '10 kHz');
plot(x, y3, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '100 kHz');
plot(x, y4, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '1000 kHz');

% 添加网格线
% grid on;
% grid minor;

% 设置坐标轴标签（放大字体）
% xlabel('Time (min)', 'FontSize', 20, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
% ylabel('SNR (dB)', 'FontSize', 20, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
xlabel('Time (min)', 'FontSize', 15, 'Fontname', 'Times New Roman');
ylabel('SNR (dB)', 'FontSize', 15, 'Fontname', 'Times New Roman');
% 添加图例（放大字体）
legend('show', 'Location', 'best', 'FontSize', 18);

% 设置坐标轴范围
xlim([0.8, 7.2]);
ylim([16.4, 16.85]);

% 设置刻度（放大字体）
set(gca, 'FontSize', 25, 'Fontname', 'Times New Roman');
set(gca, 'XTick', 1:7);
set(gca, 'YTick', 16.4:0.05:16.85);

% 美化图形 - 加粗边框
box on;
set(gca, 'LineWidth', 2.5);  % 加粗坐标轴边框

% 优化网格线
set(gca, 'GridLineStyle', '-', 'GridAlpha', 0.3, 'GridColor', [0.1, 0.1, 0.1]);

% 确保坐标轴在数据上方
set(gca, 'Layer', 'top');


%% 

% 数据定义
freq = [300, 500, 1000, 2000, 3000]; % 频率值 (Hz)
snr1_freq = [16.8833, 16.8866, 16.9572, 16.9715, 16.9811]; % 第一组SNR1
snr2_freq = [17.1641, 17.2215, 17.2284, 17.2425, 17.2965]; % 第一组SNR2
voltage = [10, 20, 30, 40, 50]; % 电压值 (mv)
snr1_volt = [16.8951, 16.9979, 17.0401, 17.0092, 16.9773]; % 第二组SNR1
snr2_volt = [17.3038, 17.4503, 17.4959, 17.3807, 17.2555]; % 第二组SNR2
% 创建图形和主坐标轴
figure;

set(gcf, 'Position', [100, 100, 900, 600]); % 放大图形尺寸 (1200x900像素)
% 主坐标轴（频率数据）
ax1 = axes;
hold(ax1, 'on'); % 对主坐标轴进行hold
% 绘制第一组数据（频率-实线）并保存句柄
p1 = plot(ax1, freq, snr1_freq, 'b-o', 'LineWidth', 6); % 增大LineWidth
p2 = plot(ax1, freq, snr2_freq, 'r-o', 'LineWidth', 6); % 增大LineWidth
% 创建第二个坐标轴（共享Y轴）
ax2 = axes('Position', ax1.Position,...
    'XAxisLocation', 'top',... % 上方横坐标
    'YAxisLocation', 'right',... % 右侧纵坐标
    'Color', 'none',... % 隐藏背景色
    'YColor', 'none'); % 隐藏右侧Y轴
hold(ax2, 'on'); % 对第二个坐标轴进行hold
% 设置虚线样式来表示不同的采样点数（通过改变 LineStyle 或 Marker）
p3 = plot(ax2, voltage, snr1_volt, 'b--s', 'LineWidth', 6, 'MarkerSize', 3); % 增大LineWidth和MarkerSize
p4 = plot(ax2, voltage, snr2_volt, 'r-.d', 'LineWidth', 6, 'MarkerSize', 3); % 增大LineWidth和MarkerSize

% grid on;
% grid minor;
% 设置坐标轴标签和范围
xlabel(ax1, 'Number of sampling points', 'FontSize', 14, 'Fontname', 'Times New Roman');
xlabel(ax2, 'Step Size (mv)', 'FontSize', 14, 'Fontname', 'Times New Roman');
ylabel(ax1, 'SNR(dB)', 'FontSize', 14, 'Fontname', 'Times New Roman');

% 显式创建图例（一次性传递所有曲线的句柄）
legend([p1, p2, p3, p4], {'X polarization with varying sampling counts', 'Y polarization with varying sampling counts', 'Different step sizes for X polarization', 'Different step sizes for Y polarization'}, ...
    'Location', 'southeast', 'FontSize', 12);
% 链接Y轴（确保缩放时同步）
linkaxes([ax1, ax2], 'y');
% 设置对数刻度
set(ax1, 'XScale', 'log'); % 频率使用对数刻度
set(ax1, 'XTick', freq); % 设置频率刻度点
set(ax2, 'XTick', voltage); % 设置电压刻度点

% 让 ax1 显示边框
box(ax1, 'on');
% 让 ax2 也显示“边框”（因为 ax2 背景透明，这里主要是让其坐标轴线条可见来模拟边框）
% 先把 ax2 的坐标轴线条颜色设为可见
set(ax2, 'XColor', 'k', 'YColor', 'k');  % 这里 'k' 表示黑色，也可根据需求改其他颜色
box(ax2, 'on');

% 若觉得 ax2 原来隐藏的右侧 Y 轴等线条显示出来影响美观，可后续再微调
% 比如只保留顶部 X 轴和右侧“虚拟”边框相关线条，不过一般这样设置就能有类似双轴都带边框的视觉效果了

