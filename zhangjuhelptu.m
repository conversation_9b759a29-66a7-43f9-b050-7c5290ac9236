clc;
clear all;
close all;

x = 1:20;

% 纵坐标数据
y = [16.391, 16.391, 16.413, 15.609, 16.199, 14.549, 10.195, 9.5442, ...
     11.78, 11.2997, 14.893, 15.273, 15.28, 15.379, 13.997, 15.549, ...
     14.796, 11.281, 15.03, 15.059];

figure;
set(gcf, 'Position', [100, 100, 600, 450]); % 设置图形大小

plot(x, y, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', '60G-16QAM');
hold on;

xlabel('Time(min)', 'FontSize', 16, 'Fontname', 'Times New Roman');
ylabel('SNR(dB)', 'FontSize', 16, 'Fontname', 'Times New Roman');
legend('show', 'Location', 'best', 'FontSize', 14);
set(gca, 'FontSize', 14, '<PERSON>ontname', 'Times New Roman');

% 设置正确的坐标轴范围
xlim([1, 20]);  % 横坐标范围 1-20
ylim([8, 17]);  % 纵坐标范围根据数据调整为8-17

% 设置坐标轴格式
grid off;
box on;
set(gca, 'LineWidth', 1.5);

% 第一个图的刻度设置
xlim([1, 20]);
ylim([8, 17]);
set(gca, 'XTick', 1:2:20); % 10个刻度 (1,3,5,7,9,11,13,15,17,19)
set(gca, 'YTick', 8:1:17); % 10个刻度 (8,9,10,11,12,13,14,15,16,17)

%% 

clc;
clear all;

% 横坐标：时间 (1-20)
x = 1:20;

% 七条光纤的SNR数据 (单位：dB)，每条光纤有20个数据
fiber1 = [16.778, 16.819, 16.899, 16.837, 16.827, 16.888, 16.856, 16.871, 16.838, 16.831, 16.806, 16.908, 16.716, 16.735, 16.835, 16.895, 16.936, 16.736, 16.778, 16.985];
fiber2 = [17.016, 17.075, 17.011, 17.172, 17.113, 16.951, 17.109, 17.096, 16.952, 17.007, 16.959, 17.066, 17.088, 17.027, 17.01, 17.064, 17.031, 17.038, 17.072, 16.964];
fiber3 = [17.318, 17.331, 17.391, 17.431, 17.423, 17.391, 17.306, 17.404, 17.37, 17.394, 17.437, 17.48, 17.396, 17.373, 17.336, 17.346, 17.3748, 17.2757, 17.408, 17.404];
fiber4 = [16.342, 16.346, 16.191, 16.254, 16.336, 16.318, 16.369, 16.35, 16.319, 16.37, 16.278, 16.435, 16.317, 16.289, 16.329, 16.296, 16.284, 16.391, 16.345, 16.176];
fiber5 = [17.117, 17.192, 17.292, 17.269, 17.231, 17.27, 17.198, 17.257, 17.285, 17.207, 17.323, 17.324, 17.295, 17.249, 17.319, 17.193, 17.263, 17.327, 17.307, 17.289];
fiber6 = [17.215, 17.227, 17.374, 17.333, 17.283, 17.232, 17.368, 17.351, 17.401, 17.356, 17.393, 17.335, 17.358, 17.126, 17.237, 17.256, 17.255, 17.302, 17.158, 17.208];
fiber7 = [17.225, 17.205, 17.124, 17.133, 17.06, 16.924, 16.943, 17.079, 17.092, 17.122, 17.001, 17.008, 17.077, 16.954, 17.022, 17.067, 17.045, 17.051, 16.997, 17.007];

% 创建图形
figure;
set(gcf, 'Position', [100, 100, 600, 450]); % 设置图形大小

% 绘制七条光纤的曲线
plot(x, fiber1, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 1');
hold on;
plot(x, fiber2, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 2');
plot(x, fiber3, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 3');
plot(x, fiber4, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 4');
plot(x, fiber5, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 5');
plot(x, fiber6, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 6');
plot(x, fiber7, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'DisplayName', 'Fiber 7');

% 设置坐标轴标签
xlabel('Time (min)', 'FontSize', 16, 'Fontname', 'Times New Roman');
ylabel('SNR (dB)', 'FontSize', 16, 'Fontname', 'Times New Roman');

% 添加图例
legend('show', 'Location', 'best', 'FontSize', 14);

% 设置坐标轴范围
xlim([0.8, 20.2]);  % 横坐标稍微扩展
ylim([16, 17.5]); % 纵坐标范围根据数据设置

% 设置刻度
set(gca, 'FontSize', 14, 'Fontname', 'Times New Roman');
set(gca, 'XTick', 1:2:20); % 明确设置横坐标刻度
set(gca, 'YTick', 16:0.1:17.5); % 设置纵坐标刻度，每隔0.1增加

% 第二个图的刻度设置  
xlim([0.8, 20.2]);
ylim([16, 17.5]);
set(gca, 'XTick', 1:2:20); % 10个刻度 (1,3,5,7,9,11,13,15,17,19)
set(gca, 'YTick', 16:0.2:17.4); % 8个刻度 (16,16.2,16.4,16.6,16.8,17.0,17.2,17.4)

% 设置坐标轴格式
grid off;
box on;
set(gca, 'LineWidth', 1.5);

%% 
clc;
clear all;

% 横坐标：时间 (1-7分钟)
x = 1:7;

% 四条曲线的SNR数据 (单位：dB)
y1 = [16.8069, 16.817, 16.809, 16.773, 16.7825, 16.77, 16.768]; % 1k频率
y2 = [16.73, 16.744, 16.685, 16.664, 16.711, 16.697, 16.721];  % 10k频率
y3 = [16.656, 16.648, 16.64, 16.573, 16.643, 16.653, 16.639];  % 100k频率
y4 = [16.532, 16.562, 16.423, 16.486, 16.573, 16.602, 16.559]; % 1000k频率

% 创建图形并设置大尺寸
figure;
set(gcf, 'Position', [100, 100, 600, 450]); % 设置图形大小

% 绘制四条曲线
plot(x, y1, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '1 kHz');
hold on;
plot(x, y2, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '10 kHz');
plot(x, y3, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '100 kHz');
plot(x, y4, 'o-', 'LineWidth', 6, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '1000 kHz');

% 设置坐标轴标签
xlabel('Time (min)', 'FontSize', 16, 'Fontname', 'Times New Roman');
ylabel('SNR (dB)', 'FontSize', 16, 'Fontname', 'Times New Roman');

% 添加图例
legend('show', 'Location', 'best', 'FontSize', 14);

% 设置坐标轴范围
xlim([0.8, 7.2]);
ylim([16.4, 16.85]);

% 设置刻度
set(gca, 'FontSize', 14, 'Fontname', 'Times New Roman');
set(gca, 'XTick', 1:7);
set(gca, 'YTick', 16.4:0.05:16.85);

% 第三个图的刻度设置
xlim([0.8, 7.2]);
ylim([16.4, 16.85]);
set(gca, 'XTick', 1:7); % 7个刻度 (1,2,3,4,5,6,7)
set(gca, 'YTick', 16.4:0.05:16.85); % 10个刻度 (16.4,16.45,16.5,...,16.85)

% 设置坐标轴格式
grid off;
box on;
set(gca, 'LineWidth', 1.5);

%% 

% 数据定义
freq = [300, 500, 1000, 2000, 3000]; % 频率值 (Hz)
snr1_freq = [16.8833, 16.8866, 16.9572, 16.9715, 16.9811]; % 第一组SNR1
snr2_freq = [17.1641, 17.2215, 17.2284, 17.2425, 17.2965]; % 第一组SNR2
voltage = [10, 20, 30, 40, 50]; % 电压值 (mv)
snr1_volt = [16.8951, 16.9979, 17.0401, 17.0092, 16.9773]; % 第二组SNR1
snr2_volt = [17.3038, 17.4503, 17.4959, 17.3807, 17.2555]; % 第二组SNR2

% 创建图形和主坐标轴
figure;
set(gcf, 'Position', [100, 100, 600, 450]); % 设置图形大小

% 主坐标轴（频率数据）
ax1 = axes;
hold(ax1, 'on');

% 绘制第一组数据（频率-实线）并保存句柄
p1 = plot(ax1, freq, snr1_freq, 'b-o', 'LineWidth', 6);
p2 = plot(ax1, freq, snr2_freq, 'r-o', 'LineWidth', 6);

% 创建第二个坐标轴（共享Y轴）
ax2 = axes('Position', ax1.Position,...
    'XAxisLocation', 'top',...
    'YAxisLocation', 'right',...
    'Color', 'none',...
    'YColor', 'none');
hold(ax2, 'on');

% 绘制第二组数据
p3 = plot(ax2, voltage, snr1_volt, 'b--s', 'LineWidth', 6, 'MarkerSize', 3);
p4 = plot(ax2, voltage, snr2_volt, 'r-.d', 'LineWidth', 6, 'MarkerSize', 3);

% 设置坐标轴标签和范围
xlabel(ax1, 'Number of sampling points', 'FontSize', 16, 'Fontname', 'Times New Roman');
xlabel(ax2, 'Step Size (mv)', 'FontSize', 16, 'Fontname', 'Times New Roman');
ylabel(ax1, 'SNR(dB)', 'FontSize', 16, 'Fontname', 'Times New Roman');

% 显式创建图例
legend([p1, p2, p3, p4], {'X polarization with varying sampling counts', 'Y polarization with varying sampling counts', 'Different step sizes for X polarization', 'Different step sizes for Y polarization'}, ...
    'Location', 'southeast', 'FontSize', 14);

% 链接Y轴
linkaxes([ax1, ax2], 'y');

% 设置对数刻度
set(ax1, 'XScale', 'log');
set(ax1, 'XTick', freq);
set(ax2, 'XTick', voltage);

% 第四个图（双坐标轴）的刻度设置
set(ax1, 'XTick', freq); % 保持对数刻度的5个点
set(ax2, 'XTick', voltage); % 保持5个电压点
% Y轴刻度由linkaxes自动同步

% 设置字体
set(ax1, 'FontSize', 14, 'Fontname', 'Times New Roman');
set(ax2, 'FontSize', 14, 'Fontname', 'Times New Roman');

% 设置坐标轴格式
grid(ax1, 'off');
grid(ax2, 'off');
box(ax1, 'on');
box(ax2, 'on');
set(ax1, 'LineWidth', 1.5);
set(ax2, 'XColor', 'k', 'YColor', 'k', 'LineWidth', 1.5);
