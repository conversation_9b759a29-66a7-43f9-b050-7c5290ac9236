clc;
clear all;
close all;

% 创建一个大的图形窗口用于subplot
figure;
set(gcf, 'Position', [100, 100, 1200, 900]); % 设置更大的图形窗口

%% 第一个子图
subplot(2, 2, 1);

x = 1:20;
% 纵坐标数据
y = [16.391, 16.391, 16.413, 15.609, 16.199, 14.549, 10.195, 9.5442, ...
     11.78, 11.2997, 14.893, 15.273, 15.28, 15.379, 13.997, 15.549, ...
     14.796, 11.281, 15.03, 15.059];

plot(x, y, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', '60G-16QAM');
hold on;

xlabel('Time(min)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
ylabel('SNR(dB)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
legend('show', 'Location', 'best', 'FontSize', 12, 'FontWeight', 'bold');
set(gca, 'FontSize', 12, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 设置坐标轴范围和刻度 - 统一为8个刻度点
xlim([1, 20]);
ylim([8, 17]);
set(gca, 'XTick', 1:3:19); % 7个刻度 (1,4,7,10,13,16,19) - 约8个点
set(gca, 'YTick', 8:1.1:16); % 8个刻度 (8,9.1,10.2,11.3,12.4,13.5,14.6,15.7) - 约8个点

% 设置坐标轴格式
grid off;
box on;
set(gca, 'LineWidth', 1.5);

%% 第二个子图
subplot(2, 2, 2);

% 横坐标：时间 (1-20)
x = 1:20;

% 七条光纤的SNR数据 (单位：dB)，每条光纤有20个数据
fiber1 = [16.778, 16.819, 16.899, 16.837, 16.827, 16.888, 16.856, 16.871, 16.838, 16.831, 16.806, 16.908, 16.716, 16.735, 16.835, 16.895, 16.936, 16.736, 16.778, 16.985];
fiber2 = [17.016, 17.075, 17.011, 17.172, 17.113, 16.951, 17.109, 17.096, 16.952, 17.007, 16.959, 17.066, 17.088, 17.027, 17.01, 17.064, 17.031, 17.038, 17.072, 16.964];
fiber3 = [17.318, 17.331, 17.391, 17.431, 17.423, 17.391, 17.306, 17.404, 17.37, 17.394, 17.437, 17.48, 17.396, 17.373, 17.336, 17.346, 17.3748, 17.2757, 17.408, 17.404];
fiber4 = [16.342, 16.346, 16.191, 16.254, 16.336, 16.318, 16.369, 16.35, 16.319, 16.37, 16.278, 16.435, 16.317, 16.289, 16.329, 16.296, 16.284, 16.391, 16.345, 16.176];
fiber5 = [17.117, 17.192, 17.292, 17.269, 17.231, 17.27, 17.198, 17.257, 17.285, 17.207, 17.323, 17.324, 17.295, 17.249, 17.319, 17.193, 17.263, 17.327, 17.307, 17.289];
fiber6 = [17.215, 17.227, 17.374, 17.333, 17.283, 17.232, 17.368, 17.351, 17.401, 17.356, 17.393, 17.335, 17.358, 17.126, 17.237, 17.256, 17.255, 17.302, 17.158, 17.208];
fiber7 = [17.225, 17.205, 17.124, 17.133, 17.06, 16.924, 16.943, 17.079, 17.092, 17.122, 17.001, 17.008, 17.077, 16.954, 17.022, 17.067, 17.045, 17.051, 16.997, 17.007];

% 绘制七条光纤的曲线 - 简化图例标签
plot(x, fiber1, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F1');
hold on;
plot(x, fiber2, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F2');
plot(x, fiber3, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F3');
plot(x, fiber4, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F4');
plot(x, fiber5, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F5');
plot(x, fiber6, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F6');
plot(x, fiber7, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'DisplayName', 'F7');

% 设置坐标轴标签
xlabel('Time (min)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
ylabel('SNR (dB)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 添加图例 - 简化排版
legend('show', 'Location', 'northeast', 'FontSize', 10, 'FontWeight', 'bold', 'NumColumns', 2);

% 设置坐标轴范围和刻度 - 统一为8个刻度点
xlim([0.8, 20.2]);
ylim([16, 17.5]);
set(gca, 'XTick', 1:3:19); % 7个刻度 (1,4,7,10,13,16,19) - 约8个点
set(gca, 'YTick', 16.0:0.2:17.4); % 8个刻度，保留一位小数 (16.0,16.2,16.4,16.6,16.8,17.0,17.2,17.4)

% 设置刻度字体
set(gca, 'FontSize', 12, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 设置坐标轴格式
grid off;
box on;
set(gca, 'LineWidth', 1.5);

%% 第三个子图
subplot(2, 2, 3);

% 横坐标：时间 (1-7分钟)
x = 1:7;

% 四条曲线的SNR数据 (单位：dB)
y1 = [16.8069, 16.817, 16.809, 16.773, 16.7825, 16.77, 16.768]; % 1k频率
y2 = [16.73, 16.744, 16.685, 16.664, 16.711, 16.697, 16.721];  % 10k频率
y3 = [16.656, 16.648, 16.64, 16.573, 16.643, 16.653, 16.639];  % 100k频率
y4 = [16.532, 16.562, 16.423, 16.486, 16.573, 16.602, 16.559]; % 1000k频率

% 绘制四条曲线 - 简化图例标签
plot(x, y1, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '1k');
hold on;
plot(x, y2, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '10k');
plot(x, y3, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '100k');
plot(x, y4, 'o-', 'LineWidth', 4, 'MarkerSize', 3, 'MarkerFaceColor', 'auto', 'DisplayName', '1000k');

% 设置坐标轴标签
xlabel('Time (min)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
ylabel('SNR (dB)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 添加图例 - 紧密排版
legend('show', 'Location', 'northeast', 'FontSize', 9, 'FontWeight', 'bold', 'NumColumns', 4);

% 设置坐标轴范围和刻度 - 统一为8个刻度点
xlim([0.8, 7.2]);
ylim([16.4, 16.9]);
set(gca, 'XTick', 1:1:7); % 7个刻度 (1,2,3,4,5,6,7) - 接近8个点
set(gca, 'YTick', 16.4:0.07:16.9); % 8个刻度，保留一位小数 (16.4,16.47,16.54,16.61,16.68,16.75,16.82,16.89)

% 设置刻度字体
set(gca, 'FontSize', 12, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 设置坐标轴格式
grid off;
box on;
set(gca, 'LineWidth', 1.5);

%% 第四个子图
subplot(2, 2, 4);

% 数据定义
freq = [300, 500, 1000, 2000, 3000]; % 频率值 (Hz)
snr1_freq = [16.8833, 16.8866, 16.9572, 16.9715, 16.9811]; % 第一组SNR1
snr2_freq = [17.1641, 17.2215, 17.2284, 17.2425, 17.2965]; % 第一组SNR2
voltage = [10, 20, 30, 40, 50]; % 电压值 (mv)
snr1_volt = [16.8951, 16.9979, 17.0401, 17.0092, 16.9773]; % 第二组SNR1
snr2_volt = [17.3038, 17.4503, 17.4959, 17.3807, 17.2555]; % 第二组SNR2

% 重新设计：创建完全独立的上下坐标轴
ax1 = gca;
hold(ax1, 'on');

% 绘制频率相关的曲线在ax1上
p1 = plot(ax1, freq, snr1_freq, 'b-o', 'LineWidth', 4, 'MarkerSize', 3);
p2 = plot(ax1, freq, snr2_freq, 'r-o', 'LineWidth', 4, 'MarkerSize', 3);

% 设置主坐标轴（下方）
xlim(ax1, [200, 3200]);
ylim(ax1, [16.8, 17.6]);
set(ax1, 'XTick', 300:500:3000); % 下方刻度：采样点数
set(ax1, 'YTick', 16.8:0.1:17.5); % 左侧刻度：SNR
set(ax1, 'TickDir', 'in');
set(ax1, 'XAxisLocation', 'bottom');
set(ax1, 'YAxisLocation', 'left');
set(ax1, 'YColor', 'k'); % 确保Y轴是黑色

% 设置下方标签
xlabel(ax1, 'Number of sampling points', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
ylabel(ax1, 'SNR(dB)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 创建第二个坐标轴（用于上方刻度和电压数据）
ax2 = axes('Position', ax1.Position,...
    'Color', 'none',...
    'XAxisLocation', 'top',...
    'YAxisLocation', 'right',...
    'TickDir', 'in');
hold(ax2, 'on');

% 绘制电压相关的曲线在ax2上
p3 = plot(ax2, voltage, snr1_volt, 'b--s', 'LineWidth', 4, 'MarkerSize', 3);
p4 = plot(ax2, voltage, snr2_volt, 'r-.d', 'LineWidth', 4, 'MarkerSize', 3);

% 设置上方坐标轴的范围和刻度
xlim(ax2, [10, 50]); % 直接使用电压范围
ylim(ax2, [16.8, 17.6]);
set(ax2, 'XTick', 10:10:50); % 上方刻度：10,20,30,40,50
set(ax2, 'XTickLabel', {'10', '20', '30', '40', '50'}); % 显示电压值
set(ax2, 'YTick', []); % 不显示右侧Y轴刻度
set(ax2, 'YColor', 'none'); % 隐藏右侧Y轴

% 设置上方标签
xlabel(ax2, 'Step Size (mv)', 'FontSize', 14, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 链接Y轴
linkaxes([ax1, ax2], 'y');

% 创建图例
legend(ax1, [p1, p2, p3, p4], {'X-samp', 'Y-samp', 'X-step', 'Y-step'}, ...
    'Location', 'northeast', 'FontSize', 8, 'FontWeight', 'bold', 'NumColumns', 2);

% 设置字体
set(ax1, 'FontSize', 12, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');
set(ax2, 'FontSize', 12, 'Fontname', 'Times New Roman', 'FontWeight', 'bold');

% 设置坐标轴格式
grid(ax1, 'off');
grid(ax2, 'off');
set(ax1, 'LineWidth', 1.5);
set(ax2, 'LineWidth', 1.5);
