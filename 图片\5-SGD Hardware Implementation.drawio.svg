<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="363px" height="349px" viewBox="-0.5 -0.5 363 349" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.6 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36&quot; version=&quot;28.0.6&quot; pages=&quot;4&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;SGD Hardware Implementation&quot; id=&quot;SGD_hw&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;788&quot; dy=&quot;980&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;800&quot; pageHeight=&quot;400&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-25&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;init&quot; target=&quot;wait_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;194&quot; y=&quot;-110&quot; /&gt;&#10;              &lt;mxPoint x=&quot;194&quot; y=&quot;-18&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;init&quot; value=&quot;Initialize&amp;lt;br&amp;gt;Va₁,₀=Vc₁,₀=Va₂,n=Vc₂,n=0V&amp;lt;br&amp;gt;n=0&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;189&quot; y=&quot;-170&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-22&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;wait_measure&quot; target=&quot;va1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wait_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;214&quot; y=&quot;-28&quot; width=&quot;90&quot; height=&quot;38&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_boundary&quot; target=&quot;va1_reset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_boundary&quot; target=&quot;va1_perturb&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_boundary&quot; value=&quot;Va₁,n at&amp;#xa;boundary?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;224&quot; y=&quot;-94&quot; width=&quot;70&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_reset&quot; value=&quot;Va₁,n+1=0V&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-105&quot; width=&quot;70&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_perturb&quot; value=&quot;Va₁,n+1=Va₁,n+ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-61&quot; width=&quot;90&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_measure&quot; target=&quot;va1_evaluate&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n+1&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;462&quot; y=&quot;-92&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_evaluate&quot; target=&quot;vc1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;503&quot; y=&quot;20&quot; /&gt;&#10;              &lt;mxPoint x=&quot;259&quot; y=&quot;20&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_evaluate&quot; target=&quot;va1_correct&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_evaluate&quot; value=&quot;Iy,n &amp;amp;gt; Iy,n+1?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;463&quot; y=&quot;-28&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_correct&quot; target=&quot;vc1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;385&quot; y=&quot;20&quot; /&gt;&#10;              &lt;mxPoint x=&quot;259&quot; y=&quot;20&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_correct&quot; value=&quot;Va₁,n+1=Va₁,n-2ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-23&quot; width=&quot;100&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_boundary&quot; value=&quot;Vc₁,n at&amp;#xa;boundary?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;224&quot; y=&quot;48&quot; width=&quot;70&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_reset&quot; value=&quot;Vc₁,n+1=0V&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;39&quot; width=&quot;70&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_perturb&quot; value=&quot;Vc₁,n+1=Vc₁,n+ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;79&quot; width=&quot;90&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_measure&quot; target=&quot;vc1_evaluate&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n+1&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;463&quot; y=&quot;53&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-20&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_evaluate&quot; target=&quot;wait_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_evaluate&quot; value=&quot;Iy,n &amp;amp;gt; Iy,n+1?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;109&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_correct&quot; value=&quot;Vc₁,n+1=Vc₁,n-2ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;114&quot; width=&quot;100&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;aux_continuous&quot; value=&quot;Continuous Update&amp;#xa;Va₂,n+1=f(Va₁,n+1)&amp;#xa;Vc₂,n+1=f(Vc₁,n+1)&amp;#xa;(Boundary Mapping)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;342&quot; y=&quot;-170&quot; width=&quot;100&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;mapping_note&quot; value=&quot;f(x): Max→4V,&amp;amp;nbsp;&amp;lt;div&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; Min→-4V,&amp;amp;nbsp;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; else→0V&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#999999;fontStyle=2;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;452&quot; y=&quot;-170&quot; width=&quot;90&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_reset&quot; target=&quot;va1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-92&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-70&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;va1_perturb&quot; target=&quot;va1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-48&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-70&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow11&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_boundary&quot; target=&quot;vc1_reset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow12&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_boundary&quot; target=&quot;vc1_perturb&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow13&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_reset&quot; target=&quot;vc1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;52&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;73&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_perturb&quot; target=&quot;vc1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;92&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;73&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow16&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; source=&quot;vc1_evaluate&quot; target=&quot;vc1_correct&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label1&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;-95&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label2&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;-55&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label3&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;443&quot; y=&quot;-24&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label4&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;47&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label5&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;89&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label6&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;442&quot; y=&quot;114&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-3&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;508&quot; y=&quot;151&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-4&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;strokeWidth=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;504&quot; y=&quot;12&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;ol4MpNASkQ_DBQMsvc1G&quot; name=&quot;Diff&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;946&quot; dy=&quot;696&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-3&quot; target=&quot;Vad89lu7M3FoXwd2GboU-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-3&quot; target=&quot;Vad89lu7M3FoXwd2GboU-7&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-3&quot; value=&quot;ADC 接口 (ADC Interface)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;243&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-4&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-5&quot; target=&quot;Vad89lu7M3FoXwd2GboU-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-5&quot; value=&quot;累加模块 1 (Accumulator 1)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;210&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-7&quot; target=&quot;Vad89lu7M3FoXwd2GboU-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-7&quot; value=&quot;累加模块 2 (Accumulator 2)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;275&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-8&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-9&quot; target=&quot;Vad89lu7M3FoXwd2GboU-11&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-9&quot; value=&quot;差分减法器 (Subtractor)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;554&quot; y=&quot;243&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-10&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-11&quot; target=&quot;Vad89lu7M3FoXwd2GboU-13&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-11&quot; value=&quot;SGD 控制器&amp;amp;nbsp;&amp;lt;div&amp;gt;(SGD Controller)&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;554&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-12&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-13&quot; target=&quot;Vad89lu7M3FoXwd2GboU-20&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-13&quot; value=&quot;DAC 接口&amp;amp;nbsp;&amp;lt;div&amp;gt;(DAC Interface)&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-14&quot; value=&quot;ADC1 In&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;200&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-15&quot; value=&quot;ADC2 In&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;300&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-16&quot; value=&quot;Iy1&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;200&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-17&quot; value=&quot;Iy2&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;300&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-18&quot; value=&quot;Iy = Iy1 - Iy2&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;605&quot; y=&quot;310&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-19&quot; value=&quot;DAC Out&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;380&quot; y=&quot;400&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-20&quot; value=&quot;Control Voltages (Va1,Vc1,Va2,Vc2)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;N12PNBKsZnTtMPG24M_2&quot; name=&quot;框图&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;3153&quot; dy=&quot;1920&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;0&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=12 12;strokeWidth=6;fillColor=#F5F5F5;strokeColor=#CCCCCC;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1395&quot; y=&quot;696&quot; width=&quot;1419&quot; height=&quot;634&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1738&quot; y=&quot;1111&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1639&quot; y=&quot;1047&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-4&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-5&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-7&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-5&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot;&amp;gt;DP-&amp;lt;/font&amp;gt;IQ&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1834&quot; y=&quot;1081&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-6&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=3;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2097&quot; y=&quot;1111&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2242&quot; y=&quot;1112.12&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;EDFA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;triangle;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2016&quot; y=&quot;1063.5&quot; width=&quot;84&quot; height=&quot;95&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2030&quot; y=&quot;1047&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-9&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-10&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-22&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;2370&quot; y=&quot;1163&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-10&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;&amp;quot;&amp;gt;ICR&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2243&quot; y=&quot;1054&quot; width=&quot;85&quot; height=&quot;217&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-11&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-12&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-14&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-12&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;background-color: rgb(213, 232, 212);&amp;quot;&amp;gt;Laser2&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1618&quot; y=&quot;1193&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-13&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;entryX=-0.006;entryY=0.783;entryDx=0;entryDy=0;entryPerimeter=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-14&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-10&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2236&quot; y=&quot;1223&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1970&quot; y=&quot;1193&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-15&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-16&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-17&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-16&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2137&quot; y=&quot;1081&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-17&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2150&quot; y=&quot;1081&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-18&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2163&quot; y=&quot;1081&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-19&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1800&quot; y=&quot;1193&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-20&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1814&quot; y=&quot;1193&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-21&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1830&quot; y=&quot;1193&quot; width=&quot;28&quot; height=&quot;28&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-22&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DSO&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2398&quot; y=&quot;1129.75&quot; width=&quot;116&quot; height=&quot;65.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-23&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;240GSa/s&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;AWG&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1821.5&quot; y=&quot;960&quot; width=&quot;145&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-24&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1855.38&quot; y=&quot;1023&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1855.38&quot; y=&quot;1080&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-25&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1880.38&quot; y=&quot;1024&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1880.38&quot; y=&quot;1081&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-26&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1907.38&quot; y=&quot;1023&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1907.38&quot; y=&quot;1080&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-27&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1931.38&quot; y=&quot;1024&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1931.38&quot; y=&quot;1081&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-28&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1638.5&quot; y=&quot;1164&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-29&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;strokeColor=#7EA6E0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1821&quot; y=&quot;993&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1605&quot; y=&quot;840&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1733&quot; y=&quot;918&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-30&quot; value=&quot;16QAM Mapping&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1483&quot; y=&quot;786&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-31&quot; value=&quot;Frame Structure Construction&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1483&quot; y=&quot;822&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-32&quot; value=&quot;Pre-Equalization&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1483&quot; y=&quot;858&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-33&quot; value=&quot;Resampling&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1483&quot; y=&quot;894&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-34&quot; value=&quot;Polarization Calibration&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;987&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-35&quot; value=&quot;CD Compensation&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;1023&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-36&quot; value=&quot;Resampling&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;1059&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-37&quot; value=&quot;DD-LMS&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;1131&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-38&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;strokeColor=#7EA6E0;fontSize=12;fontColor=#143642;fillColor=#FAE5C7;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;2454&quot; y=&quot;1129&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;2654.5000000000005&quot; y=&quot;951&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;2528&quot; y=&quot;884&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-39&quot; value=&quot;Frequency Offset Estimation&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;1095&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-40&quot; value=&quot;SNR&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;1167&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-41&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;background-color: rgb(213, 232, 212);&amp;quot;&amp;gt;Laser1&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1618&quot; y=&quot;1081&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-42&quot; value=&quot;TX-DSP&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#7EA6E0;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1483&quot; y=&quot;750&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-43&quot; value=&quot;RX-DSP&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#7EA6E0;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2600&quot; y=&quot;951&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#CCCCCC;fillColor=#F5F5F5;fontColor=#333333;dashed=1;dashPattern=12 12;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;690&quot; width=&quot;1161&quot; height=&quot;573&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;exitX=1;exitY=0.5;exitDx=0;exitDy=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;R76gVRTLNVSnkpfm54KO-5&quot; source=&quot;R76gVRTLNVSnkpfm54KO-22&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;322&quot; y=&quot;1078.2611548556433&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-4&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;R76gVRTLNVSnkpfm54KO-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;604&quot; y=&quot;1078.1969696969697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-5&quot; value=&quot;&amp;lt;p style=&amp;quot;margin-top: 0pt; margin-bottom: 0pt; direction: ltr; unicode-bidi: embed; vertical-align: baseline;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;font-family: 微软雅黑;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;铌酸锂控制器&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/p&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;arcSize=15;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;406&quot; y=&quot;1047.5&quot; width=&quot;122&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-6&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;FPGA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;arcSize=26;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;545&quot; y=&quot;832.5&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-7&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;R76gVRTLNVSnkpfm54KO-8&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;733&quot; y=&quot;1030.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-8&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;602&quot; y=&quot;1062.75&quot; width=&quot;41&quot; height=&quot;29.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-9&quot; value=&quot;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;50:50&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;div&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;保偏耦合器&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;strokeColor=none;fillColor=none;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;labelBackgroundColor=none;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;585&quot; y=&quot;1097.5&quot; width=&quot;87&quot; height=&quot;32&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-10&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;R76gVRTLNVSnkpfm54KO-11&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;643&quot; y=&quot;1081.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;712&quot; y=&quot;1129.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-11&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;734&quot; y=&quot;1114.5&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-12&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PBS&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;734&quot; y=&quot;1001.5&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-13&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;R76gVRTLNVSnkpfm54KO-15&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;825&quot; y=&quot;1026.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;875&quot; y=&quot;976.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-14&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;825&quot; y=&quot;1030.25&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;879&quot; y=&quot;1075.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-15&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;951.5&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-16&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;1044.5&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-17&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;971&quot; y=&quot;980&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1044&quot; y=&quot;980&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-18&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1043&quot; y=&quot;979.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;663&quot; y=&quot;872.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;882&quot; y=&quot;872.5&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;971&quot; y=&quot;1080.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1092&quot; y=&quot;1080.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-20&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;R76gVRTLNVSnkpfm54KO-6&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1093&quot; y=&quot;1081.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;713&quot; y=&quot;974.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1043&quot; y=&quot;849.5&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-21&quot; value=&quot;&quot; style=&quot;edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;R76gVRTLNVSnkpfm54KO-5&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;544&quot; y=&quot;865.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;594&quot; y=&quot;815.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;466&quot; y=&quot;865.5&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-22&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;Laser&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;89&quot; y=&quot;1047.5&quot; width=&quot;109&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;R76gVRTLNVSnkpfm54KO-23&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;R76gVRTLNVSnkpfm54KO-8&quot; target=&quot;R76gVRTLNVSnkpfm54KO-8&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;BJSV6wAmscUynseuFmF2-2&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;(a)&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1153&quot; y=&quot;1142.5&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;BJSV6wAmscUynseuFmF2-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot;&amp;gt;(b&amp;lt;/font&amp;gt;&amp;lt;span style=&amp;quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&amp;quot;&amp;gt;)&amp;lt;/span&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2617&quot; y=&quot;1277&quot; width=&quot;60&quot; height=&quot;43&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;2oF9lVC0x9H_EU1nuCiO&quot; name=&quot;APC&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1720&quot; dy=&quot;1265&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#E6E6E6;fillColor=#F5F5F5;fontColor=#333333;dashed=1;dashPattern=12 12;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;666&quot; y=&quot;420&quot; width=&quot;1030&quot; height=&quot;392&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;824&quot; y=&quot;699.7611548556433&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-3&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontSize=12;fontColor=#143642;startSize=8;endSize=8;fillColor=#FAE5C7;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;824&quot; y=&quot;699.7611548556433&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-5&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1106&quot; y=&quot;699.6969696969697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-6&quot; value=&quot;&amp;lt;p style=&amp;quot;margin-top: 0pt; margin-bottom: 0pt; direction: ltr; unicode-bidi: embed; vertical-align: baseline;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;font-family: 微软雅黑;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;铌酸锂控制器&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/p&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;arcSize=15;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;908&quot; y=&quot;669&quot; width=&quot;122&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;FPGA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;arcSize=26;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1047&quot; y=&quot;454&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-8&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1235&quot; y=&quot;652&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-9&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1104&quot; y=&quot;684.25&quot; width=&quot;41&quot; height=&quot;29.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-10&quot; value=&quot;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;50:50&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;div&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;保偏耦合器&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;strokeColor=none;fillColor=none;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;labelBackgroundColor=none;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1087&quot; y=&quot;719&quot; width=&quot;87&quot; height=&quot;32&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-11&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-13&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1145&quot; y=&quot;703&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1214&quot; y=&quot;751&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-13&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1236&quot; y=&quot;736&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PBS&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1236&quot; y=&quot;623&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-15&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-17&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1327&quot; y=&quot;648&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1377&quot; y=&quot;598&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-16&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1327&quot; y=&quot;651.75&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1381&quot; y=&quot;697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-17&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1382&quot; y=&quot;573&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-18&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1382&quot; y=&quot;666&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1473&quot; y=&quot;601.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1546&quot; y=&quot;601.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-20&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1545&quot; y=&quot;601&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1165&quot; y=&quot;494&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1384&quot; y=&quot;494&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-21&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1473&quot; y=&quot;702&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1594&quot; y=&quot;702&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-22&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-7&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1595&quot; y=&quot;703&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1215&quot; y=&quot;596&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1545&quot; y=&quot;471&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-23&quot; value=&quot;&quot; style=&quot;edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1046&quot; y=&quot;487&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1096&quot; y=&quot;437&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;968&quot; y=&quot;487&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-25&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;Laser&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;718&quot; y=&quot;669&quot; width=&quot;109&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-26&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-9&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="oVOXUxuvchOa5_stPHoB-25"><g><path d="M 38 61 L 8 61 L 8 152.5 L 19.76 152.5" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 25.76 152.5 L 17.76 156.5 L 19.76 152.5 L 17.76 148.5 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="init"><g><rect x="3" y="1" width="140" height="60" rx="9" ry="9" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 31px; margin-left: 4px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Initialize<br />Va₁,₀=Vc₁,₀=Va₂,n=Vc₂,n=0V<br />n=0</div></div></div></foreignObject><image x="4" y="11.5" width="138" height="42.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-22"><g><path d="M 73 143 L 73 135.24" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 73 129.24 L 77 137.24 L 73 135.24 L 69 137.24 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="wait_measure"><g><rect x="28" y="143" width="90" height="38" rx="5.7" ry="5.7" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 162px; margin-left: 29px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Wait &amp; Measure<br />Iy,n</div></div></div></foreignObject><image x="29" y="149.5" width="88" height="28.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="5RIp8YV-TdlPzCath6iw-6"><g><path d="M 108 102 L 128.5 102 L 128.5 78.5 L 140.76 78.5" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 146.76 78.5 L 138.76 82.5 L 140.76 78.5 L 138.76 74.5 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="5RIp8YV-TdlPzCath6iw-7"><g><path d="M 108 102 L 128.5 102 L 128.5 122.5 L 140.76 122.5" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 146.76 122.5 L 138.76 126.5 L 140.76 122.5 L 138.76 118.5 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="va1_boundary"><g><path d="M 73 77 L 108 102 L 73 127 L 38 102 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 102px; margin-left: 39px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Va₁,n at<br />boundary?</div></div></div></foreignObject><image x="39" y="89.5" width="68" height="28.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="va1_reset"><g><rect x="149" y="66" width="70" height="25" rx="3.75" ry="3.75" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 79px; margin-left: 150px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Va₁,n+1=0V</div></div></div></foreignObject><image x="150" y="73" width="68" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="va1_perturb"><g><rect x="149" y="110" width="90" height="25" rx="3.75" ry="3.75" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 123px; margin-left: 150px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Va₁,n+1=Va₁,n+ΔV</div></div></div></foreignObject><image x="150" y="117" width="88" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-1"><g><path d="M 316 119 L 317 119 L 317 134.76" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 317 140.76 L 313 132.76 L 317 134.76 L 321 132.76 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="va1_measure"><g><rect x="276" y="79" width="80" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 99px; margin-left: 277px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Wait &amp; Measure<br />Iy,n+1</div></div></div></foreignObject><image x="277" y="86.5" width="78" height="28.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-2"><g><path d="M 317 183 L 317 191 L 73 191 L 73 210.76" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 73 216.76 L 69 208.76 L 73 210.76 L 77 208.76 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-5"><g><path d="M 277 163 L 257.24 163" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 251.24 163 L 259.24 159 L 257.24 163 L 259.24 167 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="va1_evaluate"><g><path d="M 317 143 L 357 163 L 317 183 L 277 163 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 163px; margin-left: 278px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Iy,n &gt; Iy,n+1?</div></div></div></foreignObject><image x="278" y="157" width="78" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-14"><g><path d="M 199 178 L 199 191 L 73 191 L 73 210.76" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 73 216.76 L 69 208.76 L 73 210.76 L 77 208.76 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="va1_correct"><g><rect x="149" y="148" width="100" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 163px; margin-left: 150px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Va₁,n+1=Va₁,n-2ΔV</div></div></div></foreignObject><image x="150" y="157" width="98" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vc1_boundary"><g><path d="M 73 219 L 108 244 L 73 269 L 38 244 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 244px; margin-left: 39px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Vc₁,n at<br />boundary?</div></div></div></foreignObject><image x="39" y="231.5" width="68" height="28.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vc1_reset"><g><rect x="149" y="210" width="70" height="25" rx="3.75" ry="3.75" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 223px; margin-left: 150px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Vc₁,n+1=0V</div></div></div></foreignObject><image x="150" y="217" width="68" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vc1_perturb"><g><rect x="149" y="250" width="90" height="25" rx="3.75" ry="3.75" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 263px; margin-left: 150px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Vc₁,n+1=Vc₁,n+ΔV</div></div></div></foreignObject><image x="150" y="257" width="88" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-7"><g><path d="M 317 264 L 321 264 L 321 271.76" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 321 277.76 L 317 269.76 L 321 271.76 L 325 269.76 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vc1_measure"><g><rect x="277" y="224" width="80" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 244px; margin-left: 278px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Wait &amp; Measure<br />Iy,n+1</div></div></div></foreignObject><image x="278" y="231.5" width="78" height="28.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="oVOXUxuvchOa5_stPHoB-20"><g><path d="M 321 320 L 321 340 L 8 340 L 8 171.5 L 19.76 171.5" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 25.76 171.5 L 17.76 175.5 L 19.76 171.5 L 17.76 167.5 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vc1_evaluate"><g><path d="M 321 280 L 361 300 L 321 320 L 281 300 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 300px; margin-left: 282px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Iy,n &gt; Iy,n+1?</div></div></div></foreignObject><image x="282" y="294" width="78" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vc1_correct"><g><rect x="149" y="285" width="100" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 300px; margin-left: 150px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Vc₁,n+1=Vc₁,n-2ΔV</div></div></div></foreignObject><image x="150" y="294" width="98" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="aux_continuous"><g><rect x="156" y="1" width="100" height="60" rx="9" ry="9" fill="#f0f0f0" stroke="#666666" stroke-width="2" pointer-events="all" style="fill: rgb(240, 240, 240); stroke: rgb(102, 102, 102);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 31px; margin-left: 157px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Continuous Update<br />Va₂,n+1=f(Va₁,n+1)<br />Vc₂,n+1=f(Vc₁,n+1)<br />(Boundary Mapping)</div></div></div></foreignObject><image x="157" y="5" width="98" height="55.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="mapping_note"><g><rect x="266" y="1" width="90" height="60" rx="9" ry="9" fill="#f8f8f8" stroke="#999999" stroke-width="2" pointer-events="all" style="fill: rgb(248, 248, 248); stroke: rgb(153, 153, 153);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 31px; margin-left: 267px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-style: italic; white-space: normal; word-wrap: normal; ">f(x): Max→4V, <div>        Min→-4V, </div><div>      else→0V</div></div></div></div></foreignObject><image x="267" y="11.5" width="88" height="42.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="arrow5"><g><path d="M 219 78.5 L 248 78.5 L 248 101 L 267.76 101" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 273.76 101 L 265.76 105 L 267.76 101 L 265.76 97 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="arrow6"><g><path d="M 239 122.5 L 248 122.5 L 248 101 L 267.76 101" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 273.76 101 L 265.76 105 L 267.76 101 L 265.76 97 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="arrow11"><g><path d="M 108 244 L 128.5 244 L 128.5 222.5 L 140.76 222.5" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 146.76 222.5 L 138.76 226.5 L 140.76 222.5 L 138.76 218.5 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="arrow12"><g><path d="M 108 244 L 128.5 244 L 128.5 262.5 L 140.76 262.5" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 146.76 262.5 L 138.76 266.5 L 140.76 262.5 L 138.76 258.5 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="arrow13"><g><path d="M 219 222.5 L 248 222.5 L 248 244 L 268.76 244" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 274.76 244 L 266.76 248 L 268.76 244 L 266.76 240 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="arrow14"><g><path d="M 239 262.5 L 248 262.5 L 248 244 L 268.76 244" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 274.76 244 L 266.76 248 L 268.76 244 L 266.76 240 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="arrow16"><g><path d="M 281 300 L 261 300 L 269 300 L 257.24 300" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 251.24 300 L 259.24 296 L 257.24 300 L 259.24 304 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="label1"><g><rect x="106" y="76" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 81px; margin-left: 107px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Yes</div></div></div></foreignObject><image x="107" y="75" width="18" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAAA/CAYAAABDyo4+AAAAAXNSR0IArs4c6QAABdlJREFUeF7tmmXoLUUYxn/XAltRRMEuRAW7wO4GFRXbD3ZjFyqioqgItoIXxFZU7G7Fxg82KgYGGNiFuT+ZgXHdPbt79hw9wsy3/z2zM+8888wzz/vOnUJuAxGYkvEZjEAGqIEhGaAMUD8RyQzKDMoM6odAZlA//LIGDcGgK4HdO+D+GLAl8F3NN/MBzwALNoz5A7A28GKHucfetY5BiwAnAXvWROBi9gduBH5qEaXzLFaANBVYq9T/AeBo4GXgtxZj/atdBh0xf9sBuBaYphTVGcAJQ0S6JvAIMF2xAb8DhwMXTiIwcW1NGuTvRwFnlcC4CDgY+KMjSCsBjwMzAccAZw8xRscp+3VvAsjRZwduBjZIpvok6MXbHac/CLgA8FhtO0C3Og47vu5tAHL21QpAHg47H6O5AtgP+LVleAuEMeYuWLQ+8FLL7/7Tbm0Bst+xgNoTmxqyDXB7ixX4/akFmCcCxwNnTvrRaqtB6drnKph0D7BK8o+vBjZ82gBSZKD9Nyv064sWoE5El7YMisF6RT9YMGmGJPrTgiWoE+xZgFuChm0a9GciFt8miK4A2f884NBkcD2RmvJszYTq1CXApeHma6tZbeIfe5+uABnQPEFsl0mie6JwwFsBX5ciXjxc6z8GBr3XckXThovhEGBjYM7w3ffAk+EmvLeFf9LFHwnsGuJ2mPeB24AbANd/btBSb+Z/tGEAcpCtgVtLBtJAnCy26YFrgO2B3YCrW4KzZOir1r0JPAV4TAVqjmSMp4GdC7dfB/oWwem7cC8HgRV4ZeIIYPkw1gfA6sBIAXLxmsW9k4A/B9YtAFGIbTsC1xWifldw5LKoqW0I3BQWvUvx3WvJBy7O1OfyZGPeKgDcKLAiHXuFwPLPan43fjOBk4GxAGQwCxe78RCwaBLZnQEMbzzN4LwdPE/Topymym4IqGD+EuKwjzmfYApCak1SEGWl8Zp3jpxBcaLIkvSo7lF4oxWDkLf1PDOG42BVYCfg+gFUc0M8doJvK1cBNKJWGJYOLB+UEnmr6uytIoz0iFXpTPw3r3sBe76D51mjOJ6PhtTDhPb1AQAp2PcXu79y0seb8rLwd1pe+QrYBHiuZjzBtLyz17gAcl5vMxfnZLHpsrt4ntODw27SqLrfZYE3ni1lkH+rjYq5/q1rcv3XTo+ixSQ0jtVUREvnnDlcuybDHxe7vTnQ5MzLMXsByBabaxKwA0udZPRhwa+1rjuNCiA9UJqTdQEo3fGPgmB+2HPXFgqXxBIV47wR8koFuhGoSQAo1YxRll0V8YuDCazC+52CsfuEAl7t0Zs0gFyIrleDOYrm+tYLt9lSNQNqbo9LbMLfuk0iQF7xOu9R5myaTPPF8wsLUQVUbXVzEgCKhm2dsHVdj5nu27ThnBaUEyjr7CbOsyX91T5TkHfLY0wCQMbkzmroYtOhb1eR/JbjjwZT1sVjqaZ5i5kGfVkDmvrki0z6wmJ+ecekAqRnujtc0TFGS7qCVpfDxQcFLYbsi7svQHoezZ8JbV1btqgwWIWICfBEA1Q+ZnFRehdzqjRp9Te9k9WDU4onpHLBLt6K5oKDaubpnN5iVgsEdiwivW8413FwSwsavm9b6ELssipwX6mkEX+zhqO3+rkosywX0gzf6qpKvhGg+YPe+CJT1WYNrDW1qS0dj0KDqpyrrtbz/UoHgOyq7qgN5YfKqmFMIaxvv1D6seyrTH7VlrLXiXP5ucJdCWRfgHwzOyDQvLwoHwh9njbx7JIDqSdXAT4T1TXdsO9qVUlt1f8F8Lgp3D41Kex6Lb2P+mapxJpVZYzDAqRv8OmmbRtYlKoYxEX4pGTt26xd8E2APQpe59aA6sRbgGSMIFsaMZu3TBzLtk6ni7bw5nX/zaBFDAtQW2D+9/0yQA1bmAHKAPU75ZlBmUGZQf0QyAzqh1/WoMygzKB+CGQG9cMva1BmUGZQPwQyg/rhlzUoMygzqB8CmUH98MsalBmUGdQPgYav/wQd5zdP+zIxXgAAAABJRU5ErkJggg=="/></switch></g></g></g><g data-cell-id="label2"><g><rect x="106" y="116" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 121px; margin-left: 107px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">No</div></div></div></foreignObject><image x="107" y="115" width="18" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAAA/CAYAAABDyo4+AAAAAXNSR0IArs4c6QAABMxJREFUeF7tmkvIlUUYx39eMixDKaXErKzcWIIgpovSLmRJZZQm2aJaKF4iiVAEC7JAMcyNUSRJERZEUKJFkVpiFxE1MggK0kW2UBepkdQis+ZfMzK8vud9Z94553z6ObP6vnPm8sxvnvk/z8ycPuRSSaBP5lNNIAOq8ZAMKANKE5HsQdmDsgelEcgelMYva1AbPGgpsKqin9eBJ4C/ItZqOLALuKqmzR/AZOCbiL7bWjXUgy4AxgDPAQ+UWCCIq833/0Ra1w8YC7wJjLNtfwHmGjCfR0KPHDqseigg15vqzwTeAQTNlVPALDPZ98OGPaPWeOAL4CLgfmBzw37a3iwWkDNghvGo94C+nkXHzSRvNxP8toGVlwM7gWHALcZLv2vQR0eaNAU0CPgImFKw6jNA8H6LtNb1NwqYBByKbN+x6u0GJEObiHavBTTBrvZ1hSWMFe1eC0hb4mHgLSPcowuirWgXKra9GpA0YyTwKTDEg/QTcCfwc4BA9HpAEtWyyBYq2ucFIAn+EuDFgseEiHZTQMrF7raZ/M0mWb3Yjn0M2AKsN5F2O/B3gBeXVkmNYsWwrIl+YLeWP+BiYE2FkbGAZPc95qjyhs2d9tnoeRS4FXjULNRAO94BYLapv6cJpHYDkg1XA1sLoq0k8i5gdwsjYwDJ5qeBlwD1qyAhb/GPOZdaYA/a8XROVD0tXlTpBCAZcFOkaMcA8rVuAfBaixkPtkefO+z3dYvUlS3mDxIj2qGArrdnNt0G/Gi305EKl5hoD70646lom00Dfg11o055kMZvJdpa8SeBk56RIYDU3wum3bO23cvAopqJ9gc22O3lqlZ53RnddRKQBlOUecVeX7jBy07+IYBGGPH/2mqc+poPrAvwhOnAJq+e8jUlsX8GtP1vlZuUkAm5fotaoM+LehDSX3Gi+v/DAOOvtWCvsHUV6ZQS/BDQtiuAZIevHc4uP9MOAbTMeOQKb1KhgC4xQeNjC0XNtbVvA746mwDJljLRVjrgQrGuT6quO3TeU37jSigg6dC7dvzYtl3zoCrR1slfwq2DbQygUA3S2D5c5UtTjUdtO9s8qEq05wCP1QBaa6Ofm1dIFHN1fUC/x9xadkOkiwtVJtrSBZ2XlNO0ulGcV0gKdwD3AidqPEFzfBt4xNbbaz1I57Xa0hOAZFTZcUSfH6wAdCPwpXelokio++vva2ZZvB5eaV5LnqklYyv0FCANX3YcqQKknEqvKQ95k3ve/L28ZrJX2jc45VHR72w9CagsslUBUn15jMR1gIWieyg9LO6vgKTrEIV5zbUsi6/k2xTQUJNLSAOuSXz5VAiW2Coi1W2xVpFQAqzjQ1lmLL1TMimwMTecp6E1BeTrQepDny/adR7kIqEimoOqz3SNoddYZcmu+Fceeq1V3qR7o6gSC6hTT8VOtC8MfBeTHj1lM2v3wqsznrxa202Zu97s9LC5EVhogB6OIhMp0t34sYFE+1XzQ4X7Ih4OLwMetznUDd5LrzzxE0CepjNX7G8GkrdYk8U4J9vEbrFzcpIpRmdANfQyoAwoZYP9n13mUkEgA8pbLG2DZA/KHpQ9KI1A9qA0flmDsgdlD0ojkD0ojV/WoOxB2YPSCGQPSuOXNSh7UPagNAI1rf8FJKcgTxOXP70AAAAASUVORK5CYII="/></switch></g></g></g><g data-cell-id="label3"><g><rect x="257" y="147" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 152px; margin-left: 258px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Yes</div></div></div></foreignObject><image x="258" y="146" width="18" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="label4"><g><rect x="106" y="218" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 223px; margin-left: 107px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Yes</div></div></div></foreignObject><image x="107" y="217" width="18" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="label5"><g><rect x="106" y="260" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 265px; margin-left: 107px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">No</div></div></div></foreignObject><image x="107" y="259" width="18" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAAA/CAYAAABDyo4+AAAAAXNSR0IArs4c6QAABMxJREFUeF7tmkvIlUUYx39eMixDKaXErKzcWIIgpovSLmRJZZQm2aJaKF4iiVAEC7JAMcyNUSRJERZEUKJFkVpiFxE1MggK0kW2UBepkdQis+ZfMzK8vud9Z94553z6ObP6vnPm8sxvnvk/z8ycPuRSSaBP5lNNIAOq8ZAMKANKE5HsQdmDsgelEcgelMYva1AbPGgpsKqin9eBJ4C/ItZqOLALuKqmzR/AZOCbiL7bWjXUgy4AxgDPAQ+UWCCIq833/0Ra1w8YC7wJjLNtfwHmGjCfR0KPHDqseigg15vqzwTeAQTNlVPALDPZ98OGPaPWeOAL4CLgfmBzw37a3iwWkDNghvGo94C+nkXHzSRvNxP8toGVlwM7gWHALcZLv2vQR0eaNAU0CPgImFKw6jNA8H6LtNb1NwqYBByKbN+x6u0GJEObiHavBTTBrvZ1hSWMFe1eC0hb4mHgLSPcowuirWgXKra9GpA0YyTwKTDEg/QTcCfwc4BA9HpAEtWyyBYq2ucFIAn+EuDFgseEiHZTQMrF7raZ/M0mWb3Yjn0M2AKsN5F2O/B3gBeXVkmNYsWwrIl+YLeWP+BiYE2FkbGAZPc95qjyhs2d9tnoeRS4FXjULNRAO94BYLapv6cJpHYDkg1XA1sLoq0k8i5gdwsjYwDJ5qeBlwD1qyAhb/GPOZdaYA/a8XROVD0tXlTpBCAZcFOkaMcA8rVuAfBaixkPtkefO+z3dYvUlS3mDxIj2qGArrdnNt0G/Gi305EKl5hoD70646lom00Dfg11o055kMZvJdpa8SeBk56RIYDU3wum3bO23cvAopqJ9gc22O3lqlZ53RnddRKQBlOUecVeX7jBy07+IYBGGPH/2mqc+poPrAvwhOnAJq+e8jUlsX8GtP1vlZuUkAm5fotaoM+LehDSX3Gi+v/DAOOvtWCvsHUV6ZQS/BDQtiuAZIevHc4uP9MOAbTMeOQKb1KhgC4xQeNjC0XNtbVvA746mwDJljLRVjrgQrGuT6quO3TeU37jSigg6dC7dvzYtl3zoCrR1slfwq2DbQygUA3S2D5c5UtTjUdtO9s8qEq05wCP1QBaa6Ofm1dIFHN1fUC/x9xadkOkiwtVJtrSBZ2XlNO0ulGcV0gKdwD3AidqPEFzfBt4xNbbaz1I57Xa0hOAZFTZcUSfH6wAdCPwpXelokio++vva2ZZvB5eaV5LnqklYyv0FCANX3YcqQKknEqvKQ95k3ve/L28ZrJX2jc45VHR72w9CagsslUBUn15jMR1gIWieyg9LO6vgKTrEIV5zbUsi6/k2xTQUJNLSAOuSXz5VAiW2Coi1W2xVpFQAqzjQ1lmLL1TMimwMTecp6E1BeTrQepDny/adR7kIqEimoOqz3SNoddYZcmu+Fceeq1V3qR7o6gSC6hTT8VOtC8MfBeTHj1lM2v3wqsznrxa202Zu97s9LC5EVhogB6OIhMp0t34sYFE+1XzQ4X7Ih4OLwMetznUDd5LrzzxE0CepjNX7G8GkrdYk8U4J9vEbrFzcpIpRmdANfQyoAwoZYP9n13mUkEgA8pbLG2DZA/KHpQ9KI1A9qA0flmDsgdlD0ojkD0ojV/WoOxB2YPSCGQPSuOXNSh7UPagNAI1rf8FJKcgTxOXP70AAAAASUVORK5CYII="/></switch></g></g></g><g data-cell-id="label6"><g><rect x="256" y="285" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 290px; margin-left: 257px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Yes</div></div></div></foreignObject><image x="257" y="284" width="18" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="5RIp8YV-TdlPzCath6iw-3"><g><rect x="322" y="322" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 327px; margin-left: 323px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">No</div></div></div></foreignObject><image x="323" y="321" width="18" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAAA/CAYAAABDyo4+AAAAAXNSR0IArs4c6QAABMxJREFUeF7tmkvIlUUYx39eMixDKaXErKzcWIIgpovSLmRJZZQm2aJaKF4iiVAEC7JAMcyNUSRJERZEUKJFkVpiFxE1MggK0kW2UBepkdQis+ZfMzK8vud9Z94553z6ObP6vnPm8sxvnvk/z8ycPuRSSaBP5lNNIAOq8ZAMKANKE5HsQdmDsgelEcgelMYva1AbPGgpsKqin9eBJ4C/ItZqOLALuKqmzR/AZOCbiL7bWjXUgy4AxgDPAQ+UWCCIq833/0Ra1w8YC7wJjLNtfwHmGjCfR0KPHDqseigg15vqzwTeAQTNlVPALDPZ98OGPaPWeOAL4CLgfmBzw37a3iwWkDNghvGo94C+nkXHzSRvNxP8toGVlwM7gWHALcZLv2vQR0eaNAU0CPgImFKw6jNA8H6LtNb1NwqYBByKbN+x6u0GJEObiHavBTTBrvZ1hSWMFe1eC0hb4mHgLSPcowuirWgXKra9GpA0YyTwKTDEg/QTcCfwc4BA9HpAEtWyyBYq2ucFIAn+EuDFgseEiHZTQMrF7raZ/M0mWb3Yjn0M2AKsN5F2O/B3gBeXVkmNYsWwrIl+YLeWP+BiYE2FkbGAZPc95qjyhs2d9tnoeRS4FXjULNRAO94BYLapv6cJpHYDkg1XA1sLoq0k8i5gdwsjYwDJ5qeBlwD1qyAhb/GPOZdaYA/a8XROVD0tXlTpBCAZcFOkaMcA8rVuAfBaixkPtkefO+z3dYvUlS3mDxIj2qGArrdnNt0G/Gi305EKl5hoD70646lom00Dfg11o055kMZvJdpa8SeBk56RIYDU3wum3bO23cvAopqJ9gc22O3lqlZ53RnddRKQBlOUecVeX7jBy07+IYBGGPH/2mqc+poPrAvwhOnAJq+e8jUlsX8GtP1vlZuUkAm5fotaoM+LehDSX3Gi+v/DAOOvtWCvsHUV6ZQS/BDQtiuAZIevHc4uP9MOAbTMeOQKb1KhgC4xQeNjC0XNtbVvA746mwDJljLRVjrgQrGuT6quO3TeU37jSigg6dC7dvzYtl3zoCrR1slfwq2DbQygUA3S2D5c5UtTjUdtO9s8qEq05wCP1QBaa6Ofm1dIFHN1fUC/x9xadkOkiwtVJtrSBZ2XlNO0ulGcV0gKdwD3AidqPEFzfBt4xNbbaz1I57Xa0hOAZFTZcUSfH6wAdCPwpXelokio++vva2ZZvB5eaV5LnqklYyv0FCANX3YcqQKknEqvKQ95k3ve/L28ZrJX2jc45VHR72w9CagsslUBUn15jMR1gIWieyg9LO6vgKTrEIV5zbUsi6/k2xTQUJNLSAOuSXz5VAiW2Coi1W2xVpFQAqzjQ1lmLL1TMimwMTecp6E1BeTrQepDny/adR7kIqEimoOqz3SNoddYZcmu+Fceeq1V3qR7o6gSC6hTT8VOtC8MfBeTHj1lM2v3wqsznrxa202Zu97s9LC5EVhogB6OIhMp0t34sYFE+1XzQ4X7Ih4OLwMetznUDd5LrzzxE0CepjNX7G8GkrdYk8U4J9vEbrFzcpIpRmdANfQyoAwoZYP9n13mUkEgA8pbLG2DZA/KHpQ9KI1A9qA0flmDsgdlD0ojkD0ojV/WoOxB2YPSCGQPSuOXNSh7UPagNAI1rf8FJKcgTxOXP70AAAAASUVORK5CYII="/></switch></g></g></g><g data-cell-id="5RIp8YV-TdlPzCath6iw-4"><g><rect x="318" y="183" width="20" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 188px; margin-left: 319px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Times New Roman&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">No</div></div></div></foreignObject><image x="319" y="182" width="18" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAAA/CAYAAABDyo4+AAAAAXNSR0IArs4c6QAABMxJREFUeF7tmkvIlUUYx39eMixDKaXErKzcWIIgpovSLmRJZZQm2aJaKF4iiVAEC7JAMcyNUSRJERZEUKJFkVpiFxE1MggK0kW2UBepkdQis+ZfMzK8vud9Z94553z6ObP6vnPm8sxvnvk/z8ycPuRSSaBP5lNNIAOq8ZAMKANKE5HsQdmDsgelEcgelMYva1AbPGgpsKqin9eBJ4C/ItZqOLALuKqmzR/AZOCbiL7bWjXUgy4AxgDPAQ+UWCCIq833/0Ra1w8YC7wJjLNtfwHmGjCfR0KPHDqseigg15vqzwTeAQTNlVPALDPZ98OGPaPWeOAL4CLgfmBzw37a3iwWkDNghvGo94C+nkXHzSRvNxP8toGVlwM7gWHALcZLv2vQR0eaNAU0CPgImFKw6jNA8H6LtNb1NwqYBByKbN+x6u0GJEObiHavBTTBrvZ1hSWMFe1eC0hb4mHgLSPcowuirWgXKra9GpA0YyTwKTDEg/QTcCfwc4BA9HpAEtWyyBYq2ucFIAn+EuDFgseEiHZTQMrF7raZ/M0mWb3Yjn0M2AKsN5F2O/B3gBeXVkmNYsWwrIl+YLeWP+BiYE2FkbGAZPc95qjyhs2d9tnoeRS4FXjULNRAO94BYLapv6cJpHYDkg1XA1sLoq0k8i5gdwsjYwDJ5qeBlwD1qyAhb/GPOZdaYA/a8XROVD0tXlTpBCAZcFOkaMcA8rVuAfBaixkPtkefO+z3dYvUlS3mDxIj2qGArrdnNt0G/Gi305EKl5hoD70646lom00Dfg11o055kMZvJdpa8SeBk56RIYDU3wum3bO23cvAopqJ9gc22O3lqlZ53RnddRKQBlOUecVeX7jBy07+IYBGGPH/2mqc+poPrAvwhOnAJq+e8jUlsX8GtP1vlZuUkAm5fotaoM+LehDSX3Gi+v/DAOOvtWCvsHUV6ZQS/BDQtiuAZIevHc4uP9MOAbTMeOQKb1KhgC4xQeNjC0XNtbVvA746mwDJljLRVjrgQrGuT6quO3TeU37jSigg6dC7dvzYtl3zoCrR1slfwq2DbQygUA3S2D5c5UtTjUdtO9s8qEq05wCP1QBaa6Ofm1dIFHN1fUC/x9xadkOkiwtVJtrSBZ2XlNO0ulGcV0gKdwD3AidqPEFzfBt4xNbbaz1I57Xa0hOAZFTZcUSfH6wAdCPwpXelokio++vva2ZZvB5eaV5LnqklYyv0FCANX3YcqQKknEqvKQ95k3ve/L28ZrJX2jc45VHR72w9CagsslUBUn15jMR1gIWieyg9LO6vgKTrEIV5zbUsi6/k2xTQUJNLSAOuSXz5VAiW2Coi1W2xVpFQAqzjQ1lmLL1TMimwMTecp6E1BeTrQepDny/adR7kIqEimoOqz3SNoddYZcmu+Fceeq1V3qR7o6gSC6hTT8VOtC8MfBeTHj1lM2v3wqsznrxa202Zu97s9LC5EVhogB6OIhMp0t34sYFE+1XzQ4X7Ih4OLwMetznUDd5LrzzxE0CepjNX7G8GkrdYk8U4J9vEbrFzcpIpRmdANfQyoAwoZYP9n13mUkEgA8pbLG2DZA/KHpQ9KI1A9qA0flmDsgdlD0ojkD0ojV/WoOxB2YPSCGQPSuOXNSh7UPagNAI1rf8FJKcgTxOXP70AAAAASUVORK5CYII="/></switch></g></g></g></g></g></g></svg>