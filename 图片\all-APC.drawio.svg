<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1259px" height="448px" viewBox="-0.5 -0.5 1259 448" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.6 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36&quot; version=&quot;28.0.6&quot; pages=&quot;4&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;SGD Hardware Implementation&quot; id=&quot;SGD_hw&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;788&quot; dy=&quot;952&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;800&quot; pageHeight=&quot;400&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-25&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;init&quot; target=&quot;wait_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;194&quot; y=&quot;-110&quot; /&gt;&#10;              &lt;mxPoint x=&quot;194&quot; y=&quot;-18&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;init&quot; value=&quot;Initialize&amp;lt;br&amp;gt;Va₁,₀=Vc₁,₀=Va₂,n=Vc₂,n=0V&amp;lt;br&amp;gt;n=0&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;189&quot; y=&quot;-170&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-22&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;wait_measure&quot; target=&quot;va1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wait_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;214&quot; y=&quot;-28&quot; width=&quot;90&quot; height=&quot;38&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;&quot; parent=&quot;1&quot; source=&quot;va1_boundary&quot; target=&quot;va1_reset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=11;&quot; parent=&quot;1&quot; source=&quot;va1_boundary&quot; target=&quot;va1_perturb&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_boundary&quot; value=&quot;Va₁,n at&amp;#xa;boundary?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;224&quot; y=&quot;-94&quot; width=&quot;70&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_reset&quot; value=&quot;Va₁,n+1=0V&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-105&quot; width=&quot;70&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_perturb&quot; value=&quot;Va₁,n+1=Va₁,n+ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-61&quot; width=&quot;90&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_measure&quot; target=&quot;va1_evaluate&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n+1&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;462&quot; y=&quot;-92&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_evaluate&quot; target=&quot;vc1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;503&quot; y=&quot;20&quot; /&gt;&#10;              &lt;mxPoint x=&quot;259&quot; y=&quot;20&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_evaluate&quot; target=&quot;va1_correct&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_evaluate&quot; value=&quot;Iy,n &amp;amp;gt; Iy,n+1?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;463&quot; y=&quot;-28&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;va1_correct&quot; target=&quot;vc1_boundary&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;385&quot; y=&quot;20&quot; /&gt;&#10;              &lt;mxPoint x=&quot;259&quot; y=&quot;20&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;va1_correct&quot; value=&quot;Va₁,n+1=Va₁,n-2ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;-23&quot; width=&quot;100&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_boundary&quot; value=&quot;Vc₁,n at&amp;#xa;boundary?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;224&quot; y=&quot;48&quot; width=&quot;70&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_reset&quot; value=&quot;Vc₁,n+1=0V&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;39&quot; width=&quot;70&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_perturb&quot; value=&quot;Vc₁,n+1=Vc₁,n+ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;79&quot; width=&quot;90&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vc1_measure&quot; target=&quot;vc1_evaluate&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_measure&quot; value=&quot;Wait &amp;amp;amp; Measure&amp;#xa;Iy,n+1&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;463&quot; y=&quot;53&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;oVOXUxuvchOa5_stPHoB-20&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vc1_evaluate&quot; target=&quot;wait_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_evaluate&quot; value=&quot;Iy,n &amp;amp;gt; Iy,n+1?&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;467&quot; y=&quot;109&quot; width=&quot;80&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vc1_correct&quot; value=&quot;Vc₁,n+1=Vc₁,n-2ΔV&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;335&quot; y=&quot;114&quot; width=&quot;100&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;aux_continuous&quot; value=&quot;Continuous Update&amp;#xa;Va₂,n+1=f(Va₁,n+1)&amp;#xa;Vc₂,n+1=f(Vc₁,n+1)&amp;#xa;(Boundary Mapping)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;342&quot; y=&quot;-170&quot; width=&quot;100&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;mapping_note&quot; value=&quot;f(x): Max→4V,&amp;amp;nbsp;&amp;lt;div&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; Min→-4V,&amp;amp;nbsp;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;amp;nbsp; &amp;amp;nbsp; &amp;amp;nbsp; else→0V&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#999999;fontStyle=2;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;452&quot; y=&quot;-170&quot; width=&quot;90&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;va1_reset&quot; target=&quot;va1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-92&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-70&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;va1_perturb&quot; target=&quot;va1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-48&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;-70&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow11&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_boundary&quot; target=&quot;vc1_reset&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow12&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_boundary&quot; target=&quot;vc1_perturb&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow13&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_reset&quot; target=&quot;vc1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;52&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;73&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_perturb&quot; target=&quot;vc1_measure&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;92&quot; /&gt;&#10;              &lt;mxPoint x=&quot;434&quot; y=&quot;73&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;arrow16&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; source=&quot;vc1_evaluate&quot; target=&quot;vc1_correct&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label1&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;-95&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label2&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;-55&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label3&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;443&quot; y=&quot;-24&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label4&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;47&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label5&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;292&quot; y=&quot;89&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;label6&quot; value=&quot;Yes&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;442&quot; y=&quot;114&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-3&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;508&quot; y=&quot;151&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5RIp8YV-TdlPzCath6iw-4&quot; value=&quot;No&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontFamily=Times New Roman;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;504&quot; y=&quot;12&quot; width=&quot;20&quot; height=&quot;10&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;ol4MpNASkQ_DBQMsvc1G&quot; name=&quot;Diff&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;946&quot; dy=&quot;696&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-3&quot; target=&quot;Vad89lu7M3FoXwd2GboU-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-3&quot; target=&quot;Vad89lu7M3FoXwd2GboU-7&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-3&quot; value=&quot;ADC 接口 (ADC Interface)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;243&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-4&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-5&quot; target=&quot;Vad89lu7M3FoXwd2GboU-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-5&quot; value=&quot;累加模块 1 (Accumulator 1)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;210&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontFamily=Times New Roman;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-7&quot; target=&quot;Vad89lu7M3FoXwd2GboU-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-7&quot; value=&quot;累加模块 2 (Accumulator 2)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;275&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-8&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-9&quot; target=&quot;Vad89lu7M3FoXwd2GboU-11&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-9&quot; value=&quot;差分减法器 (Subtractor)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;554&quot; y=&quot;243&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-10&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-11&quot; target=&quot;Vad89lu7M3FoXwd2GboU-13&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-11&quot; value=&quot;SGD 控制器&amp;amp;nbsp;&amp;lt;div&amp;gt;(SGD Controller)&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;554&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-12&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontFamily=Times New Roman;strokeWidth=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;Vad89lu7M3FoXwd2GboU-13&quot; target=&quot;Vad89lu7M3FoXwd2GboU-20&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-13&quot; value=&quot;DAC 接口&amp;amp;nbsp;&amp;lt;div&amp;gt;(DAC Interface)&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;370&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-14&quot; value=&quot;ADC1 In&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;200&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-15&quot; value=&quot;ADC2 In&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;300&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-16&quot; value=&quot;Iy1&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;200&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-17&quot; value=&quot;Iy2&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;300&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-18&quot; value=&quot;Iy = Iy1 - Iy2&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;605&quot; y=&quot;310&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-19&quot; value=&quot;DAC Out&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;380&quot; y=&quot;400&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Vad89lu7M3FoXwd2GboU-20&quot; value=&quot;Control Voltages (Va1,Vc1,Va2,Vc2)&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fontFamily=Times New Roman;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;350&quot; width=&quot;100&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;N12PNBKsZnTtMPG24M_2&quot; name=&quot;框图&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1720&quot; dy=&quot;1265&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=12 12;strokeWidth=6;fillColor=#F5F5F5;strokeColor=#CCCCCC;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;477&quot; y=&quot;587&quot; width=&quot;1402&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-5&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;796&quot; y=&quot;1009&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-3&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;697&quot; y=&quot;945&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-4&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-5&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-7&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-5&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;&amp;quot;&amp;gt;DP-&amp;lt;/font&amp;gt;IQ&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;892&quot; y=&quot;979&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-6&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=3;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1155&quot; y=&quot;1009&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1300&quot; y=&quot;1010.12&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;EDFA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;triangle;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1074&quot; y=&quot;961.5&quot; width=&quot;84&quot; height=&quot;95&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-8&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1088&quot; y=&quot;945&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-9&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-10&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-22&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1428&quot; y=&quot;1061&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-10&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;&amp;quot;&amp;gt;ICR&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;fillColor=#ffe6cc;strokeColor=#d79b00;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1301&quot; y=&quot;952&quot; width=&quot;85&quot; height=&quot;217&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-11&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-12&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-14&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-12&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;background-color: rgb(213, 232, 212);&amp;quot;&amp;gt;Laser2&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;676&quot; y=&quot;1091&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-13&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;entryX=-0.006;entryY=0.783;entryDx=0;entryDy=0;entryPerimeter=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-14&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-10&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1294&quot; y=&quot;1121&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1028&quot; y=&quot;1091&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-15&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;strokeColor=#000000;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0OsqdAWfTig5Tut5cYKe-16&quot; target=&quot;0OsqdAWfTig5Tut5cYKe-17&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-16&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1195&quot; y=&quot;979&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-17&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1208&quot; y=&quot;979&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-18&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1221&quot; y=&quot;979&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-19&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;858&quot; y=&quot;1091&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-20&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;872&quot; y=&quot;1091&quot; width=&quot;29&quot; height=&quot;29&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-21&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=3;strokeColor=#000000;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;888&quot; y=&quot;1091&quot; width=&quot;28&quot; height=&quot;28&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-22&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;DSO&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeWidth=2;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1456&quot; y=&quot;1027.75&quot; width=&quot;116&quot; height=&quot;65.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-23&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;240GSa/s&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;div&amp;gt;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;AWG&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;879.5&quot; y=&quot;858&quot; width=&quot;145&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-24&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;913.38&quot; y=&quot;921&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;913.38&quot; y=&quot;978&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-25&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;938.38&quot; y=&quot;922&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;938.38&quot; y=&quot;979&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-26&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;965.38&quot; y=&quot;921&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;965.38&quot; y=&quot;978&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-27&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;strokeColor=#004C99;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;989.38&quot; y=&quot;922&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;989.38&quot; y=&quot;979&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-28&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 18px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;C band&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=16;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;696.5&quot; y=&quot;1062&quot; width=&quot;79&quot; height=&quot;34&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-29&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;strokeColor=#7EA6E0;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;879&quot; y=&quot;891&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;663&quot; y=&quot;738&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;791&quot; y=&quot;816&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-30&quot; value=&quot;16QAM Mapping&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;684&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-31&quot; value=&quot;Frame Structure Construction&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;720&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-32&quot; value=&quot;Pre-Equalization&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;756&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-33&quot; value=&quot;Resampling&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;792&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-34&quot; value=&quot;Polarization Calibration&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;885&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-35&quot; value=&quot;CD Compensation&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;921&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-36&quot; value=&quot;Resampling&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;957&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-37&quot; value=&quot;DD-LMS&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;1029&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-38&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;strokeColor=#7EA6E0;fontSize=12;fontColor=#143642;fillColor=#FAE5C7;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1512&quot; y=&quot;1027&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1712.5000000000005&quot; y=&quot;849&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1586&quot; y=&quot;782&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-39&quot; value=&quot;Frequency Offset Estimation&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;993&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-40&quot; value=&quot;SNR&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#0F8B8D;fontColor=#143642;fillColor=#FAE5C7;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;1065&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-41&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b style=&amp;quot;background-color: rgb(213, 232, 212);&amp;quot;&amp;gt;Laser1&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;676&quot; y=&quot;979&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-42&quot; value=&quot;TX-DSP&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#7EA6E0;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;541&quot; y=&quot;648&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0OsqdAWfTig5Tut5cYKe-43&quot; value=&quot;RX-DSP&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#6c8ebf;fillColor=#7EA6E0;strokeWidth=2;fontStyle=1;fontSize=14;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1658&quot; y=&quot;849&quot; width=&quot;121&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;2oF9lVC0x9H_EU1nuCiO&quot; name=&quot;APC&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1720&quot; dy=&quot;1265&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-1&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeColor=#E6E6E6;fillColor=#F5F5F5;fontColor=#333333;dashed=1;dashPattern=12 12;strokeWidth=5;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;442&quot; y=&quot;420&quot; width=&quot;1254&quot; height=&quot;443&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-2&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-4&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-3&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A8201A;fontSize=12;fontColor=#143642;startSize=8;endSize=8;fillColor=#FAE5C7;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-4&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-4&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;733&quot; y=&quot;671&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-5&quot; value=&quot;&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1106&quot; y=&quot;699.6969696969697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-6&quot; value=&quot;&amp;lt;p style=&amp;quot;margin-top: 0pt; margin-bottom: 0pt; direction: ltr; unicode-bidi: embed; vertical-align: baseline;&amp;quot;&amp;gt;&amp;lt;span style=&amp;quot;font-family: 微软雅黑;&amp;quot;&amp;gt;&amp;lt;font style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;铌酸锂控制器&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&amp;lt;/p&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;arcSize=15;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;908&quot; y=&quot;669&quot; width=&quot;122&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-7&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;FPGA&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;arcSize=26;strokeWidth=2;labelBackgroundColor=none;fillColor=#f8cecc;strokeColor=#b85450;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1047&quot; y=&quot;454&quot; width=&quot;120&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-8&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1235&quot; y=&quot;652&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-9&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1104&quot; y=&quot;684.25&quot; width=&quot;41&quot; height=&quot;29.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-10&quot; value=&quot;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;50:50&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;div&amp;gt;&amp;lt;b&amp;gt;&amp;lt;font style=&amp;quot;font-size: 15px;&amp;quot;&amp;gt;保偏耦合器&amp;lt;/font&amp;gt;&amp;lt;/b&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;text;strokeColor=none;fillColor=none;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;labelBackgroundColor=none;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1087&quot; y=&quot;719&quot; width=&quot;87&quot; height=&quot;32&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-11&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-13&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1145&quot; y=&quot;703&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1214&quot; y=&quot;751&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-12&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#A8201A;fontSize=12;fontColor=#143642;startSize=8;endSize=8;fillColor=#FAE5C7;strokeWidth=3;dashed=1;dashPattern=8 8;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-13&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1523&quot; y=&quot;765&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-13&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PC&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1236&quot; y=&quot;736&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-14&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;PBS&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1236&quot; y=&quot;623&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-15&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-17&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1327&quot; y=&quot;648&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1377&quot; y=&quot;598&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-16&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1327&quot; y=&quot;651.75&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1381&quot; y=&quot;697&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-17&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1382&quot; y=&quot;573&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-18&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;APD&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;labelBackgroundColor=none;fillColor=#FAE5C7;strokeColor=#0F8B8D;fontColor=#143642;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1382&quot; y=&quot;666&quot; width=&quot;91&quot; height=&quot;58&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-19&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1473&quot; y=&quot;601.5&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1546&quot; y=&quot;601.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-20&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1545&quot; y=&quot;601&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1165&quot; y=&quot;494&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1384&quot; y=&quot;494&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-21&quot; value=&quot;&quot; style=&quot;endArrow=none;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1473&quot; y=&quot;702&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1594&quot; y=&quot;702&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-22&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-7&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1595&quot; y=&quot;703&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1215&quot; y=&quot;596&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1545&quot; y=&quot;471&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-23&quot; value=&quot;&quot; style=&quot;edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;fontSize=12;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;labelBackgroundColor=none;strokeColor=#67AB9F;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-6&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;1046&quot; y=&quot;487&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;1096&quot; y=&quot;437&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;968&quot; y=&quot;487&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-24&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;startSize=8;endSize=8;strokeWidth=3;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-25&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-4&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-25&quot; value=&quot;&amp;lt;font style=&amp;quot;font-size: 21px;&amp;quot;&amp;gt;&amp;lt;b&amp;gt;Laser&amp;lt;/b&amp;gt;&amp;lt;/font&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;523&quot; y=&quot;671&quot; width=&quot;109&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;pNf-JO2ufqc-IShbfdLo-26&quot; style=&quot;edgeStyle=none;curved=1;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;labelBackgroundColor=none;strokeColor=#A8201A;fontColor=default;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;pNf-JO2ufqc-IShbfdLo-9&quot; target=&quot;pNf-JO2ufqc-IShbfdLo-9&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="pNf-JO2ufqc-IShbfdLo-1"><g><rect x="2" y="2" width="1254" height="443" rx="66.45" ry="66.45" fill="#f5f5f5" stroke="#e6e6e6" stroke-width="5" stroke-dasharray="60 60" pointer-events="all" style="fill: rgb(245, 245, 245); stroke: rgb(230, 230, 230);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-2"><g><path d="M 384 281.76 Q 384 281.76 456.4 281.38" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 464.65 281.34 L 453.68 286.9 L 456.4 281.38 L 453.62 275.9 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-3"><g><path d="M 384 281.76 Q 384 281.76 460.13 281.36" fill="none" stroke="#a8201a" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 466.88 281.33 L 457.91 285.87 L 460.13 281.36 L 457.86 276.87 Z" fill="#a8201a" stroke="#a8201a" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-4"><g><rect x="293" y="253" width="91" height="58" rx="8.7" ry="8.7" fill="#fae5c7" stroke="#0f8b8d" stroke-width="2" pointer-events="all" style="fill: rgb(250, 229, 199); stroke: rgb(15, 139, 141);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 282px; margin-left: 294px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #143642; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #143642; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>PC</b></font></div></div></div></foreignObject><image x="294" y="270" width="89" height="28" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-5"><g><path d="M 590 281.31 Q 590 281.31 654.4 281.64" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 662.65 281.68 L 651.62 287.12 L 654.4 281.64 L 651.67 276.12 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-6"><g><rect x="468" y="251" width="122" height="60" rx="9" ry="9" fill="#f8cecc" stroke="#b85450" stroke-width="2" pointer-events="all" style="fill: rgb(248, 206, 204); stroke: rgb(184, 84, 80);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 281px; margin-left: 469px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><p style="margin-top: 0pt; margin-bottom: 0pt; direction: ltr; unicode-bidi: embed; vertical-align: baseline;"><span style="font-family: 微软雅黑;"><font style="font-size: 20px;"><b>铌酸锂控制器</b></font></span></p></div></div></div></foreignObject><image x="469" y="269.5" width="120" height="27" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-7"><g><rect x="607" y="36" width="120" height="60" rx="15.6" ry="15.6" fill="#f8cecc" stroke="#b85450" stroke-width="2" pointer-events="all" style="fill: rgb(248, 206, 204); stroke: rgb(184, 84, 80);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 66px; margin-left: 608px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>FPGA</b></font></div></div></div></foreignObject><image x="608" y="54" width="118" height="28" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-8"><g><path d="M 705 281 Q 705 281 784.71 239.37" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 792.03 235.55 L 784.82 245.52 L 784.71 239.37 L 779.73 235.77 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-9"><g><ellipse cx="684.5" cy="281" rx="20.5" ry="14.75" fill="#fae5c7" stroke="#0f8b8d" stroke-width="2" pointer-events="all" style="fill: rgb(250, 229, 199); stroke: rgb(15, 139, 141);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-10"><g><rect x="647" y="301" width="87" height="32" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 85px; height: 1px; padding-top: 317px; margin-left: 648px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #143642; "><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: #143642; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><b><font style="font-size: 15px;">50:50</font></b><div><b><font style="font-size: 15px;">保偏耦合器</font></b></div></div></div></div></foreignObject><image x="648" y="298.5" width="85" height="42" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-11"><g><path d="M 705 285 Q 705 285 786.41 340.47" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 793.23 345.11 L 781.04 343.46 L 786.41 340.47 L 787.23 334.37 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-12"><g><path d="M 887 347 Q 887 347 1071.4 347" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" stroke-dasharray="24 24" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 1079.65 347 L 1068.65 352.5 L 1071.4 347 L 1068.65 341.5 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-13"><g><rect x="796" y="318" width="91" height="58" rx="8.7" ry="8.7" fill="#fae5c7" stroke="#0f8b8d" stroke-width="2" pointer-events="all" style="fill: rgb(250, 229, 199); stroke: rgb(15, 139, 141);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 347px; margin-left: 797px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #143642; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #143642; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>PC</b></font></div></div></div></foreignObject><image x="797" y="335" width="89" height="28" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-14"><g><rect x="796" y="205" width="91" height="58" rx="8.7" ry="8.7" fill="#fae5c7" stroke="#0f8b8d" stroke-width="2" pointer-events="all" style="fill: rgb(250, 229, 199); stroke: rgb(15, 139, 141);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 234px; margin-left: 797px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #143642; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #143642; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>PBS</b></font></div></div></div></foreignObject><image x="797" y="222" width="89" height="28" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-15"><g><path d="M 887 230 Q 887 230 933.1 191.44" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 939.43 186.15 L 934.52 197.43 L 933.1 191.44 L 927.46 188.99 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-16"><g><path d="M 887 233.75 Q 887 233.75 932.11 271.55" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 938.43 276.85 L 926.47 274 L 932.11 271.55 L 933.53 265.57 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-17"><g><rect x="942" y="155" width="91" height="58" rx="8.7" ry="8.7" fill="#fae5c7" stroke="#0f8b8d" stroke-width="2" pointer-events="all" style="fill: rgb(250, 229, 199); stroke: rgb(15, 139, 141);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 184px; margin-left: 943px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #143642; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #143642; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>APD</b></font></div></div></div></foreignObject><image x="943" y="172" width="89" height="28" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-18"><g><rect x="942" y="248" width="91" height="58" rx="8.7" ry="8.7" fill="#fae5c7" stroke="#0f8b8d" stroke-width="2" pointer-events="all" style="fill: rgb(250, 229, 199); stroke: rgb(15, 139, 141);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 277px; margin-left: 943px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #143642; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #143642; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>APD</b></font></div></div></div></foreignObject><image x="943" y="265" width="89" height="28" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAWQAAABwCAYAAAApKwoZAAAAAXNSR0IArs4c6QAAENFJREFUeF7tnX+MXFUVx895swulFNvubim/lB9qUKLxj6W7LbQ63W1aaBSj0Ab5kZiUBCNSEjXhh4lKYg1I0ISK1hiNCcgPKzQqQcDO7gJt2p3pRgKCkBCsSIF2Z7bd0B/Q3XlHX7uV7e5O373n3ffmTvudPzvnnHvO595+9807993HhA8IgAAIgIAXBNiLLJAECIAACIAAQZCxCEAABEDAEwIQZE8mAmmAAAiAAAQZawAEQAAEPCEAQfZkIpAGCIAACECQsQZAAARAwBMCEGRPJgJpgAAIgAAEGWsABEAABDwhAEH2ZCKQBgiAAAhAkLEGQAAEQMATAhBkTyYCaYAACIAABBlrAARAAAQ8IQBB9mQikAYIgAAIQJCxBkAABEDAEwIQZE8mAmmAAAiAAAQZawAEQAAEPCEAQfZkIpAGCIAACECQsQZAAARAwBMCEGRPJgJpgAAIgAAEucHXwNyOpedXafR5IjrbsBRh4eWDpcJThvbOzNraF51JueatRPQxZ0FrBuJ3iMLRw1/zkBAPMFGFmYo8Kn/fdUHrdlq/vuoyj2zro91EspeI9zNxv0i4PQx484jwwHvFQsVlXYiVHQEIcnasUxmpraP7RiJZZxVc6JHyqeH11Nc3JlhW3mrjjAUrLs+QiR+sMt091F/4JxFJnEPc9x7Vt4dIHg05uM9VbXG143s3BCDIbjjWJco5CxaccqA6fQOTLLNMYE81DBft3tb3D0u/ROYeCdZRdTDx1irTqqH+witJCvSyPqZXQ+LVQx9r6XH9iyAJK/hOTQCC3MAro6Wje0FA0kdEJynKuLNc7Pmhwk/t4qVgfVjNCIt8d/C8tvu1wuVzfUy0JReE17y7tW+7egLhmDoBCHLqiFMbgOfMW7xWmG9SjcD0akCc39Vf2KnyVzj5LFjjyllXrg6vpoGBEdsSG6C+ESG5oVLsfcDFLRpbPrCPJwBBjmfkpYWimTe5DuavlfsLj2RVYAMI1mEUTL8ujw7fZCvKDVMf0Z3l6vAa2/qyWicn8jgQ5Aad/bbO7qtJ5OEk6Qvx06fk9n/lrS1bDiSJY+rbQIJFQnxrpVi4x+ZKspHqI6Z7y6PDt0OUTVdvNnYQ5Gw4Ox0lQTNvYh4HQ+L8ULGwxWmCNYI1lGARhSHzZUP9hb+Zsmmw+lR/dExZwE5HAIKs41ZXr4TNvKNyZ5H7B0u9N9tcCWqLbzTBIqLSB8SXm+7rbcD6woBl5a7+3se0cwo/twQgyG55ZhEtWTNvcob/lhxdWtnSsyPt5BtQsEiEr6+UCg+asGnE+oioHIrkh0q9L5vUCJt0CUCQ0+XrPPrpnd1zw2irm9CnnAXPqLmnEKz9xPL5cn/vgEmtc/L5GfJe9bSwKZcjCi4KRLpJ+DpiOsvEv4ZNf7UaLNs9sHE4Lkaq9bW3N5/eNKslHD0YcNO0GUTh50TkEiJaTkQXxuUW8/36cnX4WtxPTkjRgTsE2QHELEO4aOZNzDer5l6qglVrElasyLW8OdQViPySiD6umCvj++x1qY+ITuvobj1Z5DZiuoWImhU1iohcVSn1Pq7whYtDAhBkhzDTDuWwmTcxVWPRSVJjvQQryvkjC5a1nFwdeViIltrWIES3VYo9d8f51bO+KLeWzvw5gQS/I6LuuFyn+N7qfrkiPlwMCECQDSD5YuKymTexpiyae/UWrNntS2bmcuFfiGiRzZyK8GOVU6tXx539Ue/6opqiP9rvV6etJeJVNjUess3o1pV1XieQAwS5gSa7taPrvv+dWBbtiEjjsyNHTYt2Fp/5VxrBo5g+CNacju5FQrLR7nFz3jZy0gdLhzdt2n0sNj7UF+U39ocn2jlhdaWc1a2rtNbX8RAXgtwgs2jbzBOhl5hpPxF1mpYoRDdXij0/N7W3tfNBsJS3fd6k6sj88sDz7zSCIEc5zlmQ/4RUc88RyZkW85TJrSuLfE44Uwhyg0y5bTMvEldmLls+zWe8o0CDzQdBPiRWHd13CMkaixqMdnv4Ut+Rulo7ur7FRGst6iQh+XGl2Ps9Gx/YuiMAQXbHMrVIiqu6Q8drBs3Bbq7SZiI61zC5VA+v90Ww2uZ1fYmY/mzIJDJrSEG2/VV1mIfZ7RkLdjC1IABBtoBVL1PbZt64JlTV+kS4FA+vhyBPWkFGQp9k3bV2LF7DxHdYxEg9J4tcTjhTCHIDTHlbR1d0bvEPjFMd1y2f07lkoUjYS0RNhv6pHV7viyC3zlv8VWa2eVzYSKR8qW/8PCvmn6KHacqlwu8N1wvMHBKAIDuEmUYo65+dE845Huu4P+1Dc88XwVJcNQ5JECysbN0Yveqp5seX+sYneFZ7vu1gLniWiC4yXZ9CtLZS7Fltag87dwQgyO5YphLJtpk31X5iRXMnleaeD4Kl+QNFRA23y+L/izGfb2rdl3uEWa60WKCF3PtNX9754jP7LHxg6oAABNkBxLRCKJp5o8zB4sH+jZvG59Q6f8mnOQyjf2sxzDWV5p4Pgjx2u+KPh46hN/88y9PDLw729e1ttCvkKF/FrpI3AuZLsnybjPlUHN+WNovy+CbhYXWzL85/JhcEzxPRLMP0pr6yzeeb2vYFDxDT1YZxoncwO38zdb0FedYlXec2jVJ0vvEnjTkcMpTflIu9N8T51Lu+Wvm1zeu+lliMTqwbi2F0iyaOB763JwBBtmeWmYdtM+9YD3a0zV98BYX8J4vknTf36ilYc+blzxAO/mD72PRhPTZrctWzvmNeuae0zc9iLcHUkAAE2RBU1mbWzTyiYwqoIl5UstM3U9dFsNrbm9uCWSuJ5RfRGUOKeTR+pLwu9RkU1Na5uJ2EnyOi6Qbmh02EriiXeqJzP/DJkAAEOUPYNkPZNvNMDsCx3l3g+M3UqQrWihW5uTuGW6sfHMgJN380CPizodDlzHKF8kjKQ9Nlc+hSqvXZLJ4JthDkBPAydoUgZwzcZLjz8vlpe/cH0T7Z6PBxs4/BSV22D5gcViR3b6ZWCJZZ7elZGe0/PjK8oj6r+NoyVYJM/J1ysfBT7Zjw0xGAIOu4pepl3cwzvJJV7NqIXoTp7M3UCsFKlXNccGH6UaW/5/um7xtU1JeJII+dk7yViM6Oq/nI96ZnQJvGg50ZAQiyGadMrWybeTY/qxV7kp2dAKYQrEy5TxjM+sB2RX2ZCLIiL4Ig12fpQZDrw73mqIrm25R7j2sNMLdj6flVGo220tlcLTl5cksjDHWanj0S0rLKtp6izfiK+iDINoBPAFsIsmeTbNvMIyK7p+o0e5INb4nEoVQIVlzINL7fI8IrKqVCdIi91UdRXyaCjFsWVtNYV2MIcl3xTxhcIZaaQ+XnzOu+TFietHpazUFzTyFYGc+O/EckuLJSKpQ0Ayvqy0SQNU093LLQrIDkPhDk5AydRbBu5sXsPa6V2MyFC2c3Hzz5GSK52DR5F809hWCZppfYjok2kITfHCz1vasNpqjPW0Em4m+Ui4VfaVnAT0cAgqzjloqXbTPPZO9xrURtxyKixM09hWClwvnooPwCi9wyWOqJ7qtLkgEV9fkryHgwJMlSUPtCkNXo3DoqmnmJ9ggrrsatHpKYio5CsNxC/jDaCLE8Lkw/q2ztjRp3iYT4SFhFfdkIsv2j08QhLR/c1vPXtCYAcacmAEH2ZGWo7utmn7vxY8R1FuTdRDJ2Mhu/TUwvivDrwvJCMDryUvmCM3bR+vVV1/j8FWTrw4Uy+UPhmv/xEA+C7MMsKpp59Upb00T0/QrSFUtfBVlx/OaOkMP5Q/19b7ligzhmBCDIZpxStdLcPkg1oWMHt9tmNy6Wr4LliqWn9XFbR1d09OY15nXKJmo+uLy8efN75j6wdEEAguyCYsIYigZbwhETuasPr/dUsBLBGO/sY32aHTVE9FC52HOdq3vrzgCfAIEgyHWeZM07z+qcsvrweh8FyyVLH+tTvC0m2p6Og4VcLgyLWBBkC1hpmDZIM29i6e/mhC/dWSq8YcPER8GyyT/O1sf62jq6bySSdXG5j/ve6lF8i7gwNSAAQTaAlJpJAzXzJjLQNPd8FCyXc+tbfZrT/YjolZOq4RfeHugru2SDWGYEIMhmnFKxarBm3kQG1s093wTL9aT6Vp/m/GshcnKQlGu2J0o8CHIdZ7rBmnmTLpJZePlgqfCUKULfBMs0b1M7r+qLfn3t53VEvMo0fxdPY1qMBdMpCECQ67QsZrcvmZnLhU8TUWedUkg+rOWbqb0SrOTVT4rgU31jL7XdQESBaakuzisxHQt2UxOAINdpZSiaeSLCSzXHQpqUqPwDYfVmap8Ey4SJrY0v9Y3trHiCiC6wqsHBiX5W48F4EgEIcj0Wha6ZZ33P1rY0xdtEoiGM30zti2DZcjG196A+bunsXhKIPEREbaZ5j9lZvyHFMj7MDQhAkA0guTbRNPOyOJ9W8zYRsji83gPBcj2VR8WrZ31tFy++kJh/QkzRW7ZtPyIiV1VKvY/bOsLeLQEIslueRtEUV6JWtwaMkpjaiFs7u37LQl+3imH4U7eegmVVj9I4k/pWrMjN3THcOlI9eBJRcFEg0k3C1xHTWcq0I7cnpuUOrHxry5YDCWLA1QEBCLIDiDYhlPdqn5wxPbxye1/f+zZjaWwV97aN30ydiWBpinbko6jP0ciJwpRDkfxQqfflRFHg7IQABNkJRvMgGsEjwytQ8yxqWyr/YBgdXq8QrIY6BlJRn4spSxIjDFhW7urvfSxJEPi6IwBBdscyPpKumZfoDOL4pCZbtHZ03cpEd1n6xjb3FIIFQbacBBtzIb61Uizcg0OEbKilawtBTpfvUdE1B72wyP2Dpd6bs/xPo2k6mjT3IMgZLrb4odaVq8OraWBgJN4UFlkRgCBnRZqIFM28VPce1yxddyUf+0opCHKGi+1YQzHdWx4dvh1i7Ml8jEsDgpzRnCjvzaa+97hW+Zp73XFPekGQM1pstYfZT8yryv2FR7P8xVX3qhsoAQhyRpOlETibhy5cl6F66WrMm6khyK5nySre+pDDb+O1TFbMMjeGIGeBXHcLIKu9xzUJaA4/OtY9bwhyFott0hgPUChrytt6X6vL6BjUigAE2QqXzljzBFzcz39dJnZequYeUc1dIRBkO/4JrF9jobtGw2DD7oGNwwniwDVjAhDkDIArmnmxDbIM0qbz8vlpe/cH0R7V5Tbj1Tq8HoJsQzHWdh+RDBHxkBAPkIQDochzM2fQ61k8QBSbHQxUBCDIKmxwAgEQAAH3BCDI7pkiIgiAAAioCECQVdjgBAIgAALuCUCQ3TNFRBAAARBQEYAgq7DBCQRAAATcE4Agu2eKiCAAAiCgIgBBVmGDEwiAAAi4JwBBds8UEUEABEBARQCCrMIGJxAAARBwTwCC7J4pIoIACICAigAEWYUNTiAAAiDgngAE2T1TRAQBEAABFQEIsgobnEAABEDAPQEIsnumiAgCIAACKgIQZBU2OIEACICAewIQZPdMEREEQAAEVAQgyCpscAIBEAAB9wQgyO6ZIiIIgAAIqAhAkFXY4AQCIAAC7glAkN0zRUQQAAEQUBGAIKuwwQkEQAAE3BOAILtnioggAAIgoCIAQVZhgxMIgAAIuCcAQXbPFBFBAARAQEUAgqzCBicQAAEQcE8AguyeKSKCAAiAgIoABFmFDU4gAAIg4J4ABNk9U0QEARAAARUBCLIKG5xAAARAwD0BCLJ7pogIAiAAAioCEGQVNjiBAAiAgHsCEGT3TBERBEAABFQE/guZ/oTplVmFAgAAAABJRU5ErkJggg=="/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-19"><g><path d="M 1033 183.5 Q 1033 183.5 1106 183.5" fill="none" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(103, 171, 159);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-20"><g><path d="M 1105 183 L 1105 76 L 736.6 76" fill="none" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(103, 171, 159);"/><path d="M 728.35 76 L 739.35 70.5 L 736.6 76 L 739.35 81.5 Z" fill="#67ab9f" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(103, 171, 159); stroke: rgb(103, 171, 159);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-21"><g><path d="M 1033 284 Q 1033 284 1154 284" fill="none" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(103, 171, 159);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-22"><g><path d="M 1155 285 L 1155 53 L 738.6 53" fill="none" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(103, 171, 159);"/><path d="M 730.35 53 L 741.35 47.5 L 738.6 53 L 741.35 58.5 Z" fill="#67ab9f" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(103, 171, 159); stroke: rgb(103, 171, 159);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-23"><g><path d="M 606 69 L 528 69 L 528 239.4" fill="none" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(103, 171, 159);"/><path d="M 528 247.65 L 522.5 236.65 L 528 239.4 L 533.5 236.65 Z" fill="#67ab9f" stroke="#67ab9f" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(103, 171, 159); stroke: rgb(103, 171, 159);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-24"><g><path d="M 192 282.73 Q 192 282.73 281.4 282.28" fill="none" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(168, 32, 26);"/><path d="M 289.65 282.24 L 278.67 287.8 L 281.4 282.28 L 278.62 276.8 Z" fill="#a8201a" stroke="#a8201a" stroke-width="3" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(168, 32, 26); stroke: rgb(168, 32, 26);"/></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-25"><g><rect x="83" y="253" width="109" height="60" rx="9" ry="9" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all" style="fill: rgb(213, 232, 212); stroke: rgb(130, 179, 102);"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 107px; height: 1px; padding-top: 283px; margin-left: 84px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 21px;"><b>Laser</b></font></div></div></div></foreignObject><image x="84" y="271" width="107" height="28" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="pNf-JO2ufqc-IShbfdLo-26"><g/></g></g></g></g></svg>