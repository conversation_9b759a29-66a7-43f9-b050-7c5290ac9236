<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.0//EN'
          'http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd'>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" style="fill-opacity:1; color-rendering:auto; color-interpolation:auto; text-rendering:auto; stroke:black; stroke-linecap:square; stroke-miterlimit:10; shape-rendering:auto; stroke-opacity:1; fill:black; stroke-dasharray:none; font-weight:normal; stroke-width:1; font-family:'Dialog'; font-style:normal; stroke-linejoin:miter; font-size:12px; stroke-dashoffset:0; image-rendering:auto;" width="1120" height="840" xmlns="http://www.w3.org/2000/svg"
><!--Generated by the Batik Graphics2D SVG Generator--><defs id="genericDefs"
  /><g
  ><defs id="defs1"
    ><clipPath clipPathUnits="userSpaceOnUse" id="clipPath1"
      ><path d="M0 0 L1120 0 L1120 840 L0 840 L0 0 Z"
      /></clipPath
      ><font horiz-adv-x="77.7832" id="font1"
      ><font-face ascent="91.23535" descent="19.506836" units-per-em="100" style="font-style:normal; font-family:Times New Roman; font-weight:normal;"
        /><missing-glyph horiz-adv-x="77.7832" d="M13.875 0 L13.875 62.5 L63.875 62.5 L63.875 0 L13.875 0 ZM15.4375 1.5625 L62.3125 1.5625 L62.3125 60.9375 L15.4375 60.9375 L15.4375 1.5625 Z"
        /><glyph unicode="2" horiz-adv-x="50.0" d="M45.8438 12.75 L41.2188 0 L2.1562 0 L2.1562 1.8125 Q19.3906 17.5312 26.4219 27.4922 Q33.4531 37.4531 33.4531 45.7031 Q33.4531 52 29.5938 56.0547 Q25.7344 60.1094 20.3594 60.1094 Q15.4844 60.1094 11.6016 57.25 Q7.7188 54.3906 5.8594 48.875 L4.0469 48.875 Q5.2812 57.9062 10.3281 62.7422 Q15.375 67.5781 22.9531 67.5781 Q31 67.5781 36.3984 62.4062 Q41.7969 57.2344 41.7969 50.2031 Q41.7969 45.1719 39.4531 40.1406 Q35.8438 32.2344 27.7344 23.3906 Q15.5781 10.1094 12.5469 7.375 L29.8281 7.375 Q35.1094 7.375 37.2344 7.7656 Q39.3594 8.1562 41.0703 9.3516 Q42.7812 10.5469 44.0469 12.75 L45.8438 12.75 Z"
        /><glyph unicode="4" horiz-adv-x="50.0" d="M46.5312 24.4219 L46.5312 17.4844 L37.6406 17.4844 L37.6406 0 L29.5938 0 L29.5938 17.4844 L1.5625 17.4844 L1.5625 23.7344 L32.2812 67.5781 L37.6406 67.5781 L37.6406 24.4219 L46.5312 24.4219 ZM29.5938 24.4219 L29.5938 57.2812 L6.3438 24.4219 L29.5938 24.4219 Z"
        /><glyph unicode="6" horiz-adv-x="50.0" d="M44.8281 67.5781 L44.8281 65.7656 Q38.375 65.1406 34.2969 63.2109 Q30.2188 61.2812 26.2422 57.3281 Q22.2656 53.375 19.6562 48.5156 Q17.0469 43.6562 15.2812 36.9688 Q22.3125 41.7969 29.3906 41.7969 Q36.1875 41.7969 41.1641 36.3281 Q46.1406 30.8594 46.1406 22.2656 Q46.1406 13.9688 41.1094 7.125 Q35.0625 -1.1719 25.0938 -1.1719 Q18.3125 -1.1719 13.5781 3.3281 Q4.2969 12.0625 4.2969 25.9844 Q4.2969 34.8594 7.8594 42.8672 Q11.4219 50.875 18.0391 57.0781 Q24.6562 63.2812 30.7109 65.4297 Q36.7656 67.5781 42 67.5781 L44.8281 67.5781 ZM14.4531 33.4062 Q13.5781 26.8125 13.5781 22.75 Q13.5781 18.0625 15.3125 12.5703 Q17.0469 7.0781 20.4531 3.8594 Q22.9531 1.5625 26.5156 1.5625 Q30.7656 1.5625 34.1094 5.5703 Q37.4531 9.5781 37.4531 17 Q37.4531 25.3438 34.1328 31.4453 Q30.8125 37.5469 24.7031 37.5469 Q22.8594 37.5469 20.7344 36.7656 Q18.6094 35.9844 14.4531 33.4062 Z"
        /><glyph unicode="8" horiz-adv-x="50.0" d="M19.1875 33.3438 Q11.3281 39.7969 9.0547 43.7031 Q6.7812 47.6094 6.7812 51.8125 Q6.7812 58.25 11.7656 62.9141 Q16.75 67.5781 25 67.5781 Q33.0156 67.5781 37.8984 63.2344 Q42.7812 58.8906 42.7812 53.3281 Q42.7812 49.6094 40.1406 45.75 Q37.5 41.8906 29.1562 36.6719 Q37.75 30.0312 40.5312 26.2188 Q44.2344 21.2344 44.2344 15.7188 Q44.2344 8.7344 38.9141 3.7812 Q33.5938 -1.1719 24.9531 -1.1719 Q15.5312 -1.1719 10.25 4.7344 Q6.0625 9.4688 6.0625 15.0938 Q6.0625 19.4844 9.0156 23.8047 Q11.9688 28.125 19.1875 33.3438 ZM26.8594 38.5781 Q32.7188 43.8438 34.2812 46.8984 Q35.8438 49.9531 35.8438 53.8125 Q35.8438 58.9375 32.9609 61.8438 Q30.0781 64.75 25.0938 64.75 Q20.125 64.75 17 61.8672 Q13.875 58.9844 13.875 55.125 Q13.875 52.5938 15.1641 50.0547 Q16.4531 47.5156 18.8438 45.2188 L26.8594 38.5781 ZM21.4844 31.5 Q17.4375 28.0781 15.4844 24.0469 Q13.5312 20.0156 13.5312 15.3281 Q13.5312 9.0312 16.9688 5.25 Q20.4062 1.4688 25.7344 1.4688 Q31 1.4688 34.1797 4.4453 Q37.3594 7.4219 37.3594 11.6719 Q37.3594 15.1875 35.5 17.9688 Q32.0312 23.1406 21.4844 31.5 Z"
        /><glyph unicode="0" horiz-adv-x="50.0" d="M3.6094 32.7188 Q3.6094 44.0469 7.0312 52.2266 Q10.4531 60.4062 16.1094 64.4062 Q20.5156 67.5781 25.2031 67.5781 Q32.8125 67.5781 38.875 59.8125 Q46.4375 50.2031 46.4375 33.7344 Q46.4375 22.2188 43.1172 14.1641 Q39.7969 6.1094 34.6484 2.4688 Q29.5 -1.1719 24.7031 -1.1719 Q15.2344 -1.1719 8.9375 10.0156 Q3.6094 19.4375 3.6094 32.7188 ZM13.1875 31.5 Q13.1875 17.8281 16.5469 9.1875 Q19.3438 1.9062 24.8594 1.9062 Q27.4844 1.9062 30.3203 4.2734 Q33.1562 6.6406 34.625 12.2031 Q36.8594 20.6094 36.8594 35.8906 Q36.8594 47.2188 34.5156 54.7812 Q32.7656 60.4062 29.9844 62.75 Q27.9844 64.3594 25.1406 64.3594 Q21.8281 64.3594 19.2344 61.375 Q15.7188 57.3281 14.4531 48.6328 Q13.1875 39.9375 13.1875 31.5 Z"
        /><glyph unicode="1" horiz-adv-x="50.0" d="M11.7188 59.7188 L27.8281 67.5781 L29.4375 67.5781 L29.4375 11.6719 Q29.4375 6.1094 29.9062 4.7422 Q30.375 3.375 31.8359 2.6406 Q33.2969 1.9062 37.7969 1.8125 L37.7969 0 L12.8906 0 L12.8906 1.8125 Q17.5781 1.9062 18.9453 2.6172 Q20.3125 3.3281 20.8516 4.5234 Q21.3906 5.7188 21.3906 11.6719 L21.3906 47.4062 Q21.3906 54.6406 20.9062 56.6875 Q20.5625 58.25 19.6562 58.9844 Q18.75 59.7188 17.4844 59.7188 Q15.6719 59.7188 12.4531 58.2031 L11.7188 59.7188 Z"
        /><glyph unicode=")" horiz-adv-x="33.30078" d="M2.25 67.3906 L2.25 69.4375 Q9.6719 65.7656 14.5938 60.7969 Q21.5781 53.6562 25.3906 44.0625 Q29.2031 34.4688 29.2031 24.0781 Q29.2031 8.9375 21.7578 -3.5391 Q14.3125 -16.0156 2.25 -21.3906 L2.25 -19.5781 Q8.25 -16.2188 12.1328 -10.4766 Q16.0156 -4.7344 17.8984 4.125 Q19.7812 12.9844 19.7812 22.6094 Q19.7812 33.0156 18.1719 41.6094 Q16.9375 48.3438 15.1094 52.3984 Q13.2812 56.4531 10.2578 60.2109 Q7.2344 63.9688 2.25 67.3906 Z"
        /><glyph unicode="n" horiz-adv-x="50.0" d="M16.1562 36.5781 Q24.0312 46.0469 31.1562 46.0469 Q34.8125 46.0469 37.4531 44.2188 Q40.0938 42.3906 41.6562 38.1875 Q42.7188 35.25 42.7188 29.2031 L42.7188 10.1094 Q42.7188 5.8594 43.4062 4.3438 Q43.9531 3.125 45.1484 2.4453 Q46.3438 1.7656 49.5625 1.7656 L49.5625 0 L27.4375 0 L27.4375 1.7656 L28.375 1.7656 Q31.5 1.7656 32.7422 2.7109 Q33.9844 3.6562 34.4688 5.5156 Q34.6719 6.25 34.6719 10.1094 L34.6719 28.4219 Q34.6719 34.5156 33.0859 37.2812 Q31.5 40.0469 27.7344 40.0469 Q21.9219 40.0469 16.1562 33.6875 L16.1562 10.1094 Q16.1562 5.5625 16.7031 4.5 Q17.3906 3.0781 18.5859 2.4219 Q19.7812 1.7656 23.4375 1.7656 L23.4375 0 L1.3125 0 L1.3125 1.7656 L2.2969 1.7656 Q5.7188 1.7656 6.9141 3.4922 Q8.1094 5.2188 8.1094 10.1094 L8.1094 26.7031 Q8.1094 34.7656 7.7422 36.5234 Q7.375 38.2812 6.6172 38.9141 Q5.8594 39.5469 4.5938 39.5469 Q3.2188 39.5469 1.3125 38.8125 L0.5938 40.5781 L14.0625 46.0469 L16.1562 46.0469 L16.1562 36.5781 Z"
        /><glyph unicode="(" horiz-adv-x="33.30078" d="M31.0625 -19.5781 L31.0625 -21.3906 Q23.6875 -17.6719 18.75 -12.7031 Q11.7188 -5.6094 7.9141 4.0078 Q4.1094 13.625 4.1094 23.9688 Q4.1094 39.1094 11.5781 51.5859 Q19.0469 64.0625 31.0625 69.4375 L31.0625 67.3906 Q25.0469 64.0625 21.1875 58.3047 Q17.3281 52.5469 15.4297 43.7031 Q13.5312 34.8594 13.5312 25.25 Q13.5312 14.7969 15.1406 6.25 Q16.4062 -0.4844 18.2109 -4.5625 Q20.0156 -8.6406 23.0703 -12.3984 Q26.125 -16.1562 31.0625 -19.5781 Z"
        /><glyph unicode="e" horiz-adv-x="44.384766" d="M10.6406 27.875 Q10.5938 17.9219 15.4844 12.25 Q20.3594 6.5938 26.9531 6.5938 Q31.3438 6.5938 34.5938 9.0078 Q37.8438 11.4219 40.0469 17.2812 L41.5469 16.3125 Q40.5312 9.625 35.6016 4.125 Q30.6719 -1.375 23.25 -1.375 Q15.1875 -1.375 9.4531 4.9062 Q3.7188 11.1875 3.7188 21.7812 Q3.7188 33.25 9.6016 39.6719 Q15.4844 46.0938 24.3594 46.0938 Q31.8906 46.0938 36.7188 41.1406 Q41.5469 36.1875 41.5469 27.875 L10.6406 27.875 ZM10.6406 30.7188 L31.3438 30.7188 Q31.1094 35.0156 30.3281 36.7656 Q29.1094 39.5 26.6875 41.0625 Q24.2656 42.625 21.625 42.625 Q17.5781 42.625 14.3828 39.4766 Q11.1875 36.3281 10.6406 30.7188 Z"
        /><glyph unicode="m" horiz-adv-x="77.7832" d="M16.4062 36.5312 Q21.2969 41.4062 22.1719 42.1406 Q24.3594 44 26.8984 45.0234 Q29.4375 46.0469 31.9375 46.0469 Q36.1406 46.0469 39.1641 43.6016 Q42.1875 41.1562 43.2188 36.5312 Q48.25 42.3906 51.7109 44.2188 Q55.1719 46.0469 58.8438 46.0469 Q62.4062 46.0469 65.1641 44.2188 Q67.9219 42.3906 69.5312 38.2344 Q70.6094 35.4062 70.6094 29.3438 L70.6094 10.1094 Q70.6094 5.9062 71.2344 4.3438 Q71.7344 3.2656 73.0469 2.5156 Q74.3594 1.7656 77.3438 1.7656 L77.3438 0 L55.2812 0 L55.2812 1.7656 L56.2031 1.7656 Q59.0781 1.7656 60.6875 2.875 Q61.8125 3.6562 62.3125 5.375 Q62.5 6.2031 62.5 10.1094 L62.5 29.3438 Q62.5 34.8125 61.1875 37.0625 Q59.2812 40.1875 55.0781 40.1875 Q52.4844 40.1875 49.875 38.8906 Q47.2656 37.5938 43.5625 34.0781 L43.4531 33.5469 L43.5625 31.4531 L43.5625 10.1094 Q43.5625 5.5156 44.0703 4.3906 Q44.5781 3.2656 45.9922 2.5156 Q47.4062 1.7656 50.8281 1.7656 L50.8281 0 L28.2188 0 L28.2188 1.7656 Q31.9375 1.7656 33.3281 2.6406 Q34.7188 3.5156 35.25 5.2812 Q35.5 6.1094 35.5 10.1094 L35.5 29.3438 Q35.5 34.8125 33.8906 37.2031 Q31.7344 40.3281 27.875 40.3281 Q25.25 40.3281 22.6562 38.9219 Q18.6094 36.7656 16.4062 34.0781 L16.4062 10.1094 Q16.4062 5.7188 17.0156 4.3984 Q17.625 3.0781 18.8203 2.4219 Q20.0156 1.7656 23.6875 1.7656 L23.6875 0 L1.5625 0 L1.5625 1.7656 Q4.6406 1.7656 5.8594 2.4219 Q7.0781 3.0781 7.7109 4.5156 Q8.3438 5.9531 8.3438 10.1094 L8.3438 27.2031 Q8.3438 34.5781 7.9062 36.7188 Q7.5625 38.3281 6.8359 38.9375 Q6.1094 39.5469 4.8281 39.5469 Q3.4688 39.5469 1.5625 38.8125 L0.8281 40.5781 L14.3125 46.0469 L16.4062 46.0469 L16.4062 36.5312 Z"
        /><glyph unicode="i" horiz-adv-x="27.783203" d="M14.5 69.4375 Q16.5469 69.4375 17.9922 67.9922 Q19.4375 66.5469 19.4375 64.5 Q19.4375 62.4531 17.9922 60.9844 Q16.5469 59.5156 14.5 59.5156 Q12.4531 59.5156 10.9844 60.9844 Q9.5156 62.4531 9.5156 64.5 Q9.5156 66.5469 10.9609 67.9922 Q12.4062 69.4375 14.5 69.4375 ZM18.5625 46.0469 L18.5625 10.1094 Q18.5625 5.9062 19.1719 4.5156 Q19.7812 3.125 20.9766 2.4453 Q22.1719 1.7656 25.3438 1.7656 L25.3438 0 L3.6094 0 L3.6094 1.7656 Q6.8906 1.7656 8.0078 2.3984 Q9.125 3.0312 9.7891 4.4922 Q10.4531 5.9531 10.4531 10.1094 L10.4531 27.3438 Q10.4531 34.625 10.0156 36.7656 Q9.6719 38.3281 8.9375 38.9375 Q8.2031 39.5469 6.9375 39.5469 Q5.5625 39.5469 3.6094 38.8125 L2.9375 40.5781 L16.4062 46.0469 L18.5625 46.0469 Z"
        /><glyph unicode="T" horiz-adv-x="61.083984" d="M57.8594 66.2188 L58.5938 50.6875 L56.7344 50.6875 Q56.2031 54.7812 55.2812 56.5469 Q53.7656 59.375 51.25 60.7188 Q48.7344 62.0625 44.625 62.0625 L35.2969 62.0625 L35.2969 11.4688 Q35.2969 5.375 36.625 3.8594 Q38.4844 1.8125 42.3281 1.8125 L44.625 1.8125 L44.625 0 L16.5469 0 L16.5469 1.8125 L18.8906 1.8125 Q23.0938 1.8125 24.8594 4.3438 Q25.9219 5.9062 25.9219 11.4688 L25.9219 62.0625 L17.9688 62.0625 Q13.3281 62.0625 11.375 61.375 Q8.8438 60.4531 7.0312 57.8125 Q5.2188 55.1719 4.8906 50.6875 L3.0312 50.6875 L3.8125 66.2188 L57.8594 66.2188 Z"
        /><glyph unicode="9" horiz-adv-x="50.0" d="M5.2812 -1.375 L5.2812 0.4375 Q11.625 0.5312 17.0938 3.3906 Q22.5625 6.25 27.6641 13.3828 Q32.7656 20.5156 34.7656 29.0469 Q27.0938 24.125 20.9062 24.125 Q13.9219 24.125 8.9375 29.5156 Q3.9531 34.9062 3.9531 43.8438 Q3.9531 52.5469 8.9375 59.3281 Q14.9375 67.5781 24.6094 67.5781 Q32.7656 67.5781 38.5781 60.8438 Q45.7031 52.4844 45.7031 40.2344 Q45.7031 29.2031 40.2812 19.6562 Q34.8594 10.1094 25.2031 3.8125 Q17.3281 -1.375 8.0625 -1.375 L5.2812 -1.375 ZM35.5469 32.6719 Q36.4219 39.0156 36.4219 42.8281 Q36.4219 47.5625 34.8125 53.0547 Q33.2031 58.5469 30.25 61.4766 Q27.2969 64.4062 23.5312 64.4062 Q19.1875 64.4062 15.9141 60.5 Q12.6406 56.5938 12.6406 48.875 Q12.6406 38.5781 17 32.7656 Q20.1719 28.5625 24.8125 28.5625 Q27.0469 28.5625 30.125 29.6406 Q33.2031 30.7188 35.5469 32.6719 Z"
        /><glyph unicode="3" horiz-adv-x="50.0" d="M5.0781 53.6094 Q7.9062 60.2969 12.2266 63.9375 Q16.5469 67.5781 23 67.5781 Q30.9531 67.5781 35.2031 62.4062 Q38.4219 58.5469 38.4219 54.1562 Q38.4219 46.9219 29.3438 39.2031 Q35.4531 36.8125 38.5781 32.375 Q41.7031 27.9375 41.7031 21.9219 Q41.7031 13.3281 36.2344 7.0312 Q29.1094 -1.1719 15.5781 -1.1719 Q8.8906 -1.1719 6.4688 0.4922 Q4.0469 2.1562 4.0469 4.0469 Q4.0469 5.4688 5.1953 6.5469 Q6.3438 7.625 7.9531 7.625 Q9.1875 7.625 10.4531 7.2344 Q11.2812 6.9844 14.2109 5.4453 Q17.1406 3.9062 18.2656 3.6094 Q20.0625 3.0781 22.125 3.0781 Q27.0938 3.0781 30.7812 6.9375 Q34.4688 10.7969 34.4688 16.0625 Q34.4688 19.9219 32.7656 23.5781 Q31.5 26.3125 29.9844 27.7344 Q27.875 29.6875 24.2188 31.2734 Q20.5625 32.8594 16.75 32.8594 L15.1875 32.8594 L15.1875 34.3281 Q19.0469 34.8125 22.9297 37.1094 Q26.8125 39.4062 28.5703 42.625 Q30.3281 45.8438 30.3281 49.7031 Q30.3281 54.7344 27.1797 57.8359 Q24.0312 60.9375 19.3438 60.9375 Q11.7656 60.9375 6.6875 52.8281 L5.0781 53.6094 Z"
        /><glyph unicode="5" horiz-adv-x="50.0" d="M43.4062 66.2188 L39.5938 57.9062 L19.6719 57.9062 L15.3281 49.0312 Q28.2656 47.125 35.8438 39.4062 Q42.3281 32.7656 42.3281 23.7812 Q42.3281 18.5625 40.2109 14.1172 Q38.0938 9.6719 34.8672 6.5469 Q31.6406 3.4219 27.6875 1.5156 Q22.0781 -1.1719 16.1562 -1.1719 Q10.2031 -1.1719 7.4922 0.8516 Q4.7812 2.875 4.7812 5.3281 Q4.7812 6.6875 5.9062 7.7422 Q7.0312 8.7969 8.7344 8.7969 Q10.0156 8.7969 10.9688 8.4062 Q11.9219 8.0156 14.2031 6.3906 Q17.875 3.8594 21.625 3.8594 Q27.3438 3.8594 31.6641 8.1797 Q35.9844 12.5 35.9844 18.7031 Q35.9844 24.7031 32.125 29.9062 Q28.2656 35.1094 21.4844 37.9375 Q16.1562 40.1406 6.9844 40.4844 L19.6719 66.2188 L43.4062 66.2188 Z"
        /><glyph unicode="7" horiz-adv-x="50.0" d="M10.0625 66.2188 L45.5625 66.2188 L45.5625 64.3594 L23.4844 -1.375 L18.0156 -1.375 L37.7969 58.25 L19.5781 58.25 Q14.0625 58.25 11.7188 56.9375 Q7.625 54.6875 5.125 50 L3.7188 50.5312 L10.0625 66.2188 Z"
        /><glyph unicode="B" horiz-adv-x="66.69922" d="M46.1875 33.7969 Q53.0781 32.3281 56.5 29.1094 Q61.2344 24.6094 61.2344 18.1094 Q61.2344 13.1875 58.1094 8.6719 Q54.9844 4.1562 49.5391 2.0781 Q44.0938 0 32.9062 0 L1.6562 0 L1.6562 1.8125 L4.1562 1.8125 Q8.2969 1.8125 10.1094 4.4375 Q11.2344 6.1562 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.6406 9.8125 62.25 Q7.9062 64.4062 4.1562 64.4062 L1.6562 64.4062 L1.6562 66.2188 L30.2812 66.2188 Q38.2812 66.2188 43.1094 65.0469 Q50.4375 63.2812 54.2969 58.8125 Q58.1562 54.3438 58.1562 48.5312 Q58.1562 43.5625 55.125 39.625 Q52.0938 35.6875 46.1875 33.7969 ZM20.6094 36.4219 Q22.4062 36.0781 24.7266 35.9141 Q27.0469 35.75 29.8281 35.75 Q36.9688 35.75 40.5547 37.2812 Q44.1406 38.8125 46.0469 41.9922 Q47.9531 45.1719 47.9531 48.9219 Q47.9531 54.7344 43.2188 58.8359 Q38.4844 62.9375 29.3906 62.9375 Q24.5156 62.9375 20.6094 61.8594 L20.6094 36.4219 ZM20.6094 4.7812 Q26.2656 3.4688 31.7812 3.4688 Q40.625 3.4688 45.2656 7.4453 Q49.9062 11.4219 49.9062 17.2812 Q49.9062 21.1406 47.8047 24.7031 Q45.7031 28.2656 40.9688 30.3203 Q36.2344 32.375 29.25 32.375 Q26.2188 32.375 24.0703 32.2734 Q21.9219 32.1719 20.6094 31.9375 L20.6094 4.7812 Z"
        /><glyph unicode="d" horiz-adv-x="50.0" d="M34.7188 5.0312 Q31.4531 1.6094 28.3281 0.1172 Q25.2031 -1.375 21.5781 -1.375 Q14.2656 -1.375 8.7969 4.7578 Q3.3281 10.8906 3.3281 20.5156 Q3.3281 30.125 9.3828 38.1094 Q15.4375 46.0938 24.9531 46.0938 Q30.8594 46.0938 34.7188 42.3281 L34.7188 50.5938 Q34.7188 58.25 34.3516 60.0078 Q33.9844 61.7656 33.2031 62.3984 Q32.4219 63.0312 31.25 63.0312 Q29.9844 63.0312 27.875 62.25 L27.25 63.9688 L40.5781 69.4375 L42.7812 69.4375 L42.7812 17.7188 Q42.7812 9.8594 43.1406 8.125 Q43.5 6.3906 44.3125 5.7109 Q45.125 5.0312 46.1875 5.0312 Q47.5156 5.0312 49.7031 5.8594 L50.25 4.1562 L36.9688 -1.375 L34.7188 -1.375 L34.7188 5.0312 ZM34.7188 8.4531 L34.7188 31.5 Q34.4219 34.8125 32.9609 37.5469 Q31.5 40.2812 29.0781 41.6719 Q26.6562 43.0625 24.3594 43.0625 Q20.0625 43.0625 16.7031 39.2031 Q12.25 34.125 12.25 24.3594 Q12.25 14.5 16.5469 9.25 Q20.8438 4 26.125 4 Q30.5625 4 34.7188 8.4531 Z"
        /><glyph unicode="R" horiz-adv-x="66.69922" d="M67.5781 0 L49.9062 0 L27.4844 30.9531 Q25 30.8594 23.4375 30.8594 Q22.7969 30.8594 22.0703 30.8828 Q21.3438 30.9062 20.5625 30.9531 L20.5625 11.7188 Q20.5625 5.4688 21.9219 3.9531 Q23.7812 1.8125 27.4844 1.8125 L30.0781 1.8125 L30.0781 0 L1.7031 0 L1.7031 1.8125 L4.2031 1.8125 Q8.4062 1.8125 10.2031 4.5469 Q11.2344 6.0625 11.2344 11.7188 L11.2344 54.5 Q11.2344 60.75 9.8594 62.25 Q7.9531 64.4062 4.2031 64.4062 L1.7031 64.4062 L1.7031 66.2188 L25.8281 66.2188 Q36.375 66.2188 41.3828 64.6797 Q46.3906 63.1406 49.8828 59.0156 Q53.375 54.8906 53.375 49.1719 Q53.375 43.0625 49.3906 38.5703 Q45.4062 34.0781 37.0625 32.2344 L50.7344 13.2344 Q55.4219 6.6875 58.7891 4.5391 Q62.1562 2.3906 67.5781 1.8125 L67.5781 0 ZM20.5625 34.0312 Q21.4844 34.0312 22.1719 34.0078 Q22.8594 33.9844 23.2969 33.9844 Q32.7656 33.9844 37.5781 38.0859 Q42.3906 42.1875 42.3906 48.5312 Q42.3906 54.7344 38.5078 58.6172 Q34.625 62.5 28.2188 62.5 Q25.3906 62.5 20.5625 61.5781 L20.5625 34.0312 Z"
        /><glyph unicode="N" horiz-adv-x="72.2168" d="M-1.3125 66.2188 L16.6562 66.2188 L57.125 16.5469 L57.125 54.7344 Q57.125 60.8438 55.7656 62.3594 Q53.9531 64.4062 50.0469 64.4062 L47.75 64.4062 L47.75 66.2188 L70.7969 66.2188 L70.7969 64.4062 L68.4531 64.4062 Q64.2656 64.4062 62.5 61.8594 Q61.4219 60.2969 61.4219 54.7344 L61.4219 -1.0781 L59.6719 -1.0781 L16.0156 52.25 L16.0156 11.4688 Q16.0156 5.375 17.3281 3.8594 Q19.1875 1.8125 23.0469 1.8125 L25.3906 1.8125 L25.3906 0 L2.3438 0 L2.3438 1.8125 L4.6406 1.8125 Q8.8906 1.8125 10.6406 4.3438 Q11.7188 5.9062 11.7188 11.4688 L11.7188 57.5156 Q8.8438 60.8906 7.3516 61.9609 Q5.8594 63.0312 2.9844 63.9688 Q1.5625 64.4062 -1.3125 64.4062 L-1.3125 66.2188 Z"
        /><glyph unicode="S" horiz-adv-x="55.615234" d="M45.8438 67.7188 L45.8438 44.8281 L44.0469 44.8281 Q43.1719 51.4219 40.8984 55.3281 Q38.625 59.2344 34.4219 61.5234 Q30.2188 63.8125 25.7344 63.8125 Q20.6562 63.8125 17.3359 60.7188 Q14.0156 57.625 14.0156 53.6562 Q14.0156 50.6406 16.1094 48.1406 Q19.1406 44.4844 30.5156 38.375 Q39.7969 33.4062 43.1875 30.7422 Q46.5781 28.0781 48.4141 24.4609 Q50.25 20.8438 50.25 16.8906 Q50.25 9.375 44.4141 3.9297 Q38.5781 -1.5156 29.3906 -1.5156 Q26.5156 -1.5156 23.9688 -1.0781 Q22.4688 -0.8281 17.7031 0.7109 Q12.9375 2.25 11.6719 2.25 Q10.4531 2.25 9.7422 1.5156 Q9.0312 0.7812 8.6875 -1.5156 L6.8906 -1.5156 L6.8906 21.1875 L8.6875 21.1875 Q9.9688 14.0625 12.1172 10.5234 Q14.2656 6.9844 18.6797 4.6406 Q23.0938 2.2969 28.375 2.2969 Q34.4688 2.2969 38.0078 5.5156 Q41.5469 8.7344 41.5469 13.1406 Q41.5469 15.5781 40.2109 18.0703 Q38.875 20.5625 36.0312 22.7031 Q34.125 24.1719 25.6328 28.9297 Q17.1406 33.6875 13.5547 36.5234 Q9.9688 39.3594 8.1094 42.7734 Q6.25 46.1875 6.25 50.2969 Q6.25 57.4219 11.7188 62.5703 Q17.1875 67.7188 25.6406 67.7188 Q30.9062 67.7188 36.8125 65.1406 Q39.5469 63.9219 40.6719 63.9219 Q41.9375 63.9219 42.75 64.6797 Q43.5625 65.4375 44.0469 67.7188 L45.8438 67.7188 Z"
        /><glyph unicode="M" horiz-adv-x="88.916016" d="M40.9219 0 L15.3281 55.7188 L15.3281 11.4688 Q15.3281 5.375 16.6562 3.8594 Q18.4531 1.8125 22.3594 1.8125 L24.7031 1.8125 L24.7031 0 L1.6562 0 L1.6562 1.8125 L4 1.8125 Q8.2031 1.8125 9.9688 4.3438 Q11.0312 5.9062 11.0312 11.4688 L11.0312 54.7344 Q11.0312 59.125 10.0625 61.0781 Q9.375 62.5 7.5469 63.4531 Q5.7188 64.4062 1.6562 64.4062 L1.6562 66.2188 L20.4062 66.2188 L44.4375 14.4062 L68.0625 66.2188 L86.8125 66.2188 L86.8125 64.4062 L84.5156 64.4062 Q80.2812 64.4062 78.5156 61.8594 Q77.4375 60.2969 77.4375 54.7344 L77.4375 11.4688 Q77.4375 5.375 78.8125 3.8594 Q80.6094 1.8125 84.5156 1.8125 L86.8125 1.8125 L86.8125 0 L58.6875 0 L58.6875 1.8125 L61.0312 1.8125 Q65.2812 1.8125 67 4.3438 Q68.0625 5.9062 68.0625 11.4688 L68.0625 55.7188 L42.5312 0 L40.9219 0 Z"
        /><glyph unicode="A" horiz-adv-x="72.2168" d="M45.75 22.1719 L20.125 22.1719 L15.625 11.7188 Q13.9688 7.8594 13.9688 5.9531 Q13.9688 4.4375 15.4062 3.2969 Q16.8438 2.1562 21.625 1.8125 L21.625 0 L0.7812 0 L0.7812 1.8125 Q4.9375 2.5469 6.1562 3.7188 Q8.6406 6.0625 11.6719 13.2344 L34.9688 67.7188 L36.6719 67.7188 L59.7188 12.6406 Q62.5 6 64.7734 4.0234 Q67.0469 2.0469 71.0938 1.8125 L71.0938 0 L44.9688 0 L44.9688 1.8125 Q48.9219 2 50.3125 3.125 Q51.7031 4.25 51.7031 5.8594 Q51.7031 8.0156 49.75 12.6406 L45.75 22.1719 ZM44.3906 25.7812 L33.1562 52.5469 L21.625 25.7812 L44.3906 25.7812 Z"
        /><glyph unicode="Q" horiz-adv-x="72.2168" d="M44.0469 -0.7344 Q49.0312 -9.3281 54.8125 -13.3828 Q60.5938 -17.4375 67.9688 -18.0156 L67.9688 -19.5781 Q61.2344 -19.3438 53.5703 -16.875 Q45.9062 -14.4062 39.0391 -10.0391 Q32.1719 -5.6719 27.2031 -0.7344 Q20.1719 2.0938 16.0625 5.2812 Q10.1094 10.0156 6.8125 16.9219 Q3.5156 23.8281 3.5156 33.25 Q3.5156 48.0938 13.1328 57.9062 Q22.75 67.7188 36.4219 67.7188 Q49.4219 67.7188 58.9141 57.8828 Q68.4062 48.0469 68.4062 33.0625 Q68.4062 20.9062 61.6484 11.7266 Q54.8906 2.5469 44.0469 -0.7344 ZM35.8438 63.9688 Q26.9531 63.9688 21.5312 57.625 Q14.7031 49.6562 14.7031 33.25 Q14.7031 17.1875 21.625 8.5 Q27 1.8125 35.8438 1.8125 Q45.0156 1.8125 50.6406 8.5 Q57.2344 16.4062 57.2344 31.8438 Q57.2344 43.7031 53.6094 51.8125 Q50.8281 58.0625 46.2188 61.0156 Q41.6094 63.9688 35.8438 63.9688 Z"
        /><glyph unicode="-" horiz-adv-x="33.30078" d="M4.0469 26.125 L29.2969 26.125 L29.2969 18.75 L4.0469 18.75 L4.0469 26.125 Z"
        /><glyph unicode="G" horiz-adv-x="72.2168" d="M61.375 67.7188 L63.0938 46.8281 L61.375 46.8281 Q58.7969 54.6406 54.6875 58.5938 Q48.7812 64.3125 39.5 64.3125 Q26.8594 64.3125 20.2656 54.2969 Q14.75 45.8438 14.75 34.1875 Q14.75 24.7031 18.4141 16.8906 Q22.0781 9.0781 28.0078 5.4453 Q33.9375 1.8125 40.1875 1.8125 Q43.8438 1.8125 47.2656 2.7344 Q50.6875 3.6562 53.8594 5.4688 L53.8594 24.6094 Q53.8594 29.5938 53.1016 31.1328 Q52.3438 32.6719 50.7578 33.4766 Q49.1719 34.2812 45.1719 34.2812 L45.1719 36.1406 L70.7969 36.1406 L70.7969 34.2812 L69.5781 34.2812 Q65.7656 34.2812 64.3594 31.7344 Q63.375 29.9375 63.375 24.6094 L63.375 4.3438 Q57.7656 1.3125 52.2969 -0.1016 Q46.8281 -1.5156 40.1406 -1.5156 Q20.9531 -1.5156 10.9844 10.7969 Q3.5156 20.0156 3.5156 32.0781 Q3.5156 40.8281 7.7188 48.8281 Q12.7031 58.3438 21.3906 63.4844 Q28.6562 67.7188 38.5781 67.7188 Q42.1875 67.7188 45.1406 67.1328 Q48.0938 66.5469 53.5156 64.5469 Q56.25 63.5312 57.1719 63.5312 Q58.1094 63.5312 58.7656 64.3828 Q59.4219 65.2344 59.5781 67.7188 L61.375 67.7188 Z"
      /></font
    ></defs
    ><g style="fill:white; stroke:white;"
    ><rect x="0" y="0" width="1120" style="clip-path:url(#clipPath1); stroke:none;" height="840"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><rect x="0" width="1120" height="840" y="0" style="stroke:none;"
      /><path style="stroke:none;" d="M146 748 L1014 748 L1014 63 L146 63 Z"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="63" style="fill:none;" x1="146" x2="146" y1="748"
      /><line y2="63" style="fill:none;" x1="168.8421" x2="168.8421" y1="748"
      /><line y2="63" style="fill:none;" x1="214.5263" x2="214.5263" y1="748"
      /><line y2="63" style="fill:none;" x1="237.3684" x2="237.3684" y1="748"
      /><line y2="63" style="fill:none;" x1="260.2105" x2="260.2105" y1="748"
      /><line y2="63" style="fill:none;" x1="305.8947" x2="305.8947" y1="748"
      /><line y2="63" style="fill:none;" x1="328.7368" x2="328.7368" y1="748"
      /><line y2="63" style="fill:none;" x1="351.5789" x2="351.5789" y1="748"
      /><line y2="63" style="fill:none;" x1="397.2632" x2="397.2632" y1="748"
      /><line y2="63" style="fill:none;" x1="420.1053" x2="420.1053" y1="748"
      /><line y2="63" style="fill:none;" x1="442.9474" x2="442.9474" y1="748"
      /><line y2="63" style="fill:none;" x1="488.6316" x2="488.6316" y1="748"
      /><line y2="63" style="fill:none;" x1="511.4737" x2="511.4737" y1="748"
      /><line y2="63" style="fill:none;" x1="534.3158" x2="534.3158" y1="748"
      /><line y2="63" style="fill:none;" x1="580" x2="580" y1="748"
      /><line y2="63" style="fill:none;" x1="602.8421" x2="602.8421" y1="748"
      /><line y2="63" style="fill:none;" x1="625.6842" x2="625.6842" y1="748"
      /><line y2="63" style="fill:none;" x1="671.3684" x2="671.3684" y1="748"
      /><line y2="63" style="fill:none;" x1="694.2105" x2="694.2105" y1="748"
      /><line y2="63" style="fill:none;" x1="717.0526" x2="717.0526" y1="748"
      /><line y2="63" style="fill:none;" x1="762.7368" x2="762.7368" y1="748"
      /><line y2="63" style="fill:none;" x1="785.5789" x2="785.5789" y1="748"
      /><line y2="63" style="fill:none;" x1="808.4211" x2="808.4211" y1="748"
      /><line y2="63" style="fill:none;" x1="854.1053" x2="854.1053" y1="748"
      /><line y2="63" style="fill:none;" x1="876.9474" x2="876.9474" y1="748"
      /><line y2="63" style="fill:none;" x1="899.7895" x2="899.7895" y1="748"
      /><line y2="63" style="fill:none;" x1="945.4737" x2="945.4737" y1="748"
      /><line y2="63" style="fill:none;" x1="968.3158" x2="968.3158" y1="748"
      /><line y2="63" style="fill:none;" x1="991.1579" x2="991.1579" y1="748"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="63" style="fill:none;" x1="191.6842" x2="191.6842" y1="748"
      /><line y2="63" style="fill:none;" x1="283.0526" x2="283.0526" y1="748"
      /><line y2="63" style="fill:none;" x1="374.4211" x2="374.4211" y1="748"
      /><line y2="63" style="fill:none;" x1="465.7895" x2="465.7895" y1="748"
      /><line y2="63" style="fill:none;" x1="557.1579" x2="557.1579" y1="748"
      /><line y2="63" style="fill:none;" x1="648.5263" x2="648.5263" y1="748"
      /><line y2="63" style="fill:none;" x1="739.8947" x2="739.8947" y1="748"
      /><line y2="63" style="fill:none;" x1="831.2632" x2="831.2632" y1="748"
      /><line y2="63" style="fill:none;" x1="922.6316" x2="922.6316" y1="748"
      /><line y2="63" style="fill:none;" x1="1014" x2="1014" y1="748"
    /></g
    ><g style="fill-opacity:0.251; color-rendering:optimizeQuality; color-interpolation:linearRGB; text-rendering:geometricPrecision; stroke:rgb(26,26,26); stroke-linecap:butt; stroke-miterlimit:1; stroke-opacity:0.251; fill:rgb(26,26,26); stroke-dasharray:1,3; stroke-width:1.3333; stroke-linejoin:bevel; image-rendering:optimizeQuality;"
    ><line y2="732.7778" style="fill:none;" x1="1014" x2="146" y1="732.7778"
      /><line y2="717.5556" style="fill:none;" x1="1014" x2="146" y1="717.5556"
      /><line y2="702.3333" style="fill:none;" x1="1014" x2="146" y1="702.3333"
      /><line y2="687.1111" style="fill:none;" x1="1014" x2="146" y1="687.1111"
      /><line y2="656.6667" style="fill:none;" x1="1014" x2="146" y1="656.6667"
      /><line y2="641.4445" style="fill:none;" x1="1014" x2="146" y1="641.4445"
      /><line y2="626.2222" style="fill:none;" x1="1014" x2="146" y1="626.2222"
      /><line y2="611" style="fill:none;" x1="1014" x2="146" y1="611"
      /><line y2="580.5555" style="fill:none;" x1="1014" x2="146" y1="580.5555"
      /><line y2="565.3334" style="fill:none;" x1="1014" x2="146" y1="565.3334"
      /><line y2="550.1111" style="fill:none;" x1="1014" x2="146" y1="550.1111"
      /><line y2="534.8889" style="fill:none;" x1="1014" x2="146" y1="534.8889"
      /><line y2="504.4445" style="fill:none;" x1="1014" x2="146" y1="504.4445"
      /><line y2="489.2223" style="fill:none;" x1="1014" x2="146" y1="489.2223"
      /><line y2="474" style="fill:none;" x1="1014" x2="146" y1="474"
      /><line y2="458.7778" style="fill:none;" x1="1014" x2="146" y1="458.7778"
      /><line y2="428.3333" style="fill:none;" x1="1014" x2="146" y1="428.3333"
      /><line y2="413.1111" style="fill:none;" x1="1014" x2="146" y1="413.1111"
      /><line y2="397.8889" style="fill:none;" x1="1014" x2="146" y1="397.8889"
      /><line y2="382.6667" style="fill:none;" x1="1014" x2="146" y1="382.6667"
      /><line y2="352.2222" style="fill:none;" x1="1014" x2="146" y1="352.2222"
      /><line y2="337" style="fill:none;" x1="1014" x2="146" y1="337"
      /><line y2="321.7777" style="fill:none;" x1="1014" x2="146" y1="321.7777"
      /><line y2="306.5555" style="fill:none;" x1="1014" x2="146" y1="306.5555"
      /><line y2="276.1111" style="fill:none;" x1="1014" x2="146" y1="276.1111"
      /><line y2="260.8889" style="fill:none;" x1="1014" x2="146" y1="260.8889"
      /><line y2="245.6666" style="fill:none;" x1="1014" x2="146" y1="245.6666"
      /><line y2="230.4444" style="fill:none;" x1="1014" x2="146" y1="230.4444"
      /><line y2="200" style="fill:none;" x1="1014" x2="146" y1="200"
      /><line y2="184.7778" style="fill:none;" x1="1014" x2="146" y1="184.7778"
      /><line y2="169.5555" style="fill:none;" x1="1014" x2="146" y1="169.5555"
      /><line y2="154.3333" style="fill:none;" x1="1014" x2="146" y1="154.3333"
      /><line y2="123.8888" style="fill:none;" x1="1014" x2="146" y1="123.8888"
      /><line y2="108.6667" style="fill:none;" x1="1014" x2="146" y1="108.6667"
      /><line y2="93.4444" style="fill:none;" x1="1014" x2="146" y1="93.4444"
      /><line y2="78.2223" style="fill:none;" x1="1014" x2="146" y1="78.2223"
    /></g
    ><g style="stroke-linecap:butt; fill-opacity:0.149; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333; stroke-opacity:0.149;"
    ><line y2="748" style="fill:none;" x1="1014" x2="146" y1="748"
      /><line y2="671.8889" style="fill:none;" x1="1014" x2="146" y1="671.8889"
      /><line y2="595.7778" style="fill:none;" x1="1014" x2="146" y1="595.7778"
      /><line y2="519.6667" style="fill:none;" x1="1014" x2="146" y1="519.6667"
      /><line y2="443.5555" style="fill:none;" x1="1014" x2="146" y1="443.5555"
      /><line y2="367.4445" style="fill:none;" x1="1014" x2="146" y1="367.4445"
      /><line y2="291.3333" style="fill:none;" x1="1014" x2="146" y1="291.3333"
      /><line y2="215.2222" style="fill:none;" x1="1014" x2="146" y1="215.2222"
      /><line y2="139.1111" style="fill:none;" x1="1014" x2="146" y1="139.1111"
      /><line y2="63" style="fill:none;" x1="1014" x2="146" y1="63"
      /><line x1="146" x2="1014" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="748"
      /><line x1="146" x2="1014" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="63"
      /><line x1="191.6842" x2="191.6842" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="283.0526" x2="283.0526" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="374.4211" x2="374.4211" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="465.7895" x2="465.7895" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="557.1579" x2="557.1579" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="648.5263" x2="648.5263" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="739.8947" x2="739.8947" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="831.2632" x2="831.2632" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="922.6316" x2="922.6316" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="1014" x2="1014" y1="748" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="739.3199"
      /><line x1="191.6842" x2="191.6842" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="283.0526" x2="283.0526" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="374.4211" x2="374.4211" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="465.7895" x2="465.7895" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="557.1579" x2="557.1579" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="648.5263" x2="648.5263" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="739.8947" x2="739.8947" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="831.2632" x2="831.2632" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="922.6316" x2="922.6316" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
      /><line x1="1014" x2="1014" y1="63" style="stroke-linecap:square; fill-opacity:1; fill:none; stroke-opacity:1;" y2="71.68"
    /></g
    ><g transform="translate(191.6842,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-8" xml:space="preserve" y="30" style="stroke:none;"
      >2</text
    ></g
    ><g transform="translate(283.0526,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-8" xml:space="preserve" y="30" style="stroke:none;"
      >4</text
    ></g
    ><g transform="translate(374.4211,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-8" xml:space="preserve" y="30" style="stroke:none;"
      >6</text
    ></g
    ><g transform="translate(465.7895,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-8" xml:space="preserve" y="30" style="stroke:none;"
      >8</text
    ></g
    ><g transform="translate(557.1579,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="30" style="stroke:none;"
      >10</text
    ></g
    ><g transform="translate(648.5263,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="30" style="stroke:none;"
      >12</text
    ></g
    ><g transform="translate(739.8947,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="30" style="stroke:none;"
      >14</text
    ></g
    ><g transform="translate(831.2632,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="30" style="stroke:none;"
      >16</text
    ></g
    ><g transform="translate(922.6316,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="30" style="stroke:none;"
      >18</text
    ></g
    ><g transform="translate(1014,759.7333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="30" style="stroke:none;"
      >20</text
    ></g
    ><g transform="translate(580.0004,799.4)" style="font-size:35.2px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-76.5" xml:space="preserve" y="33" style="stroke:none;"
      >Time(min)</text
    ></g
    ><g style="fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><line y2="63" style="fill:none;" x1="146" x2="146" y1="748"
      /><line y2="63" style="fill:none;" x1="1014" x2="1014" y1="748"
      /><line y2="748" style="fill:none;" x1="146" x2="154.68" y1="748"
      /><line y2="671.8889" style="fill:none;" x1="146" x2="154.68" y1="671.8889"
      /><line y2="595.7778" style="fill:none;" x1="146" x2="154.68" y1="595.7778"
      /><line y2="519.6667" style="fill:none;" x1="146" x2="154.68" y1="519.6667"
      /><line y2="443.5555" style="fill:none;" x1="146" x2="154.68" y1="443.5555"
      /><line y2="367.4445" style="fill:none;" x1="146" x2="154.68" y1="367.4445"
      /><line y2="291.3333" style="fill:none;" x1="146" x2="154.68" y1="291.3333"
      /><line y2="215.2222" style="fill:none;" x1="146" x2="154.68" y1="215.2222"
      /><line y2="139.1111" style="fill:none;" x1="146" x2="154.68" y1="139.1111"
      /><line y2="63" style="fill:none;" x1="146" x2="154.68" y1="63"
      /><line y2="748" style="fill:none;" x1="1014" x2="1005.3199" y1="748"
      /><line y2="671.8889" style="fill:none;" x1="1014" x2="1005.3199" y1="671.8889"
      /><line y2="595.7778" style="fill:none;" x1="1014" x2="1005.3199" y1="595.7778"
      /><line y2="519.6667" style="fill:none;" x1="1014" x2="1005.3199" y1="519.6667"
      /><line y2="443.5555" style="fill:none;" x1="1014" x2="1005.3199" y1="443.5555"
      /><line y2="367.4445" style="fill:none;" x1="1014" x2="1005.3199" y1="367.4445"
      /><line y2="291.3333" style="fill:none;" x1="1014" x2="1005.3199" y1="291.3333"
      /><line y2="215.2222" style="fill:none;" x1="1014" x2="1005.3199" y1="215.2222"
      /><line y2="139.1111" style="fill:none;" x1="1014" x2="1005.3199" y1="139.1111"
      /><line y2="63" style="fill:none;" x1="1014" x2="1005.3199" y1="63"
    /></g
    ><g transform="translate(134.2667,748)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="11.5" style="stroke:none;"
      >8</text
    ></g
    ><g transform="translate(134.2667,671.8889)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-16" xml:space="preserve" y="11.5" style="stroke:none;"
      >9</text
    ></g
    ><g transform="translate(134.2667,595.7778)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >10</text
    ></g
    ><g transform="translate(134.2667,519.6667)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >11</text
    ></g
    ><g transform="translate(134.2667,443.5555)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >12</text
    ></g
    ><g transform="translate(134.2667,367.4445)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >13</text
    ></g
    ><g transform="translate(134.2667,291.3333)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >14</text
    ></g
    ><g transform="translate(134.2667,215.2222)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >15</text
    ></g
    ><g transform="translate(134.2667,139.1111)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >16</text
    ></g
    ><g transform="translate(134.2667,63)" style="font-size:32px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-32" xml:space="preserve" y="11.5" style="stroke:none;"
      >17</text
    ></g
    ><g transform="translate(94.2667,405.4997) rotate(-90)" style="font-size:35.2px; fill:rgb(38,38,38); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; font-family:'Times New Roman'; stroke:rgb(38,38,38); color-interpolation:linearRGB;"
    ><text x="-66.5" xml:space="preserve" y="-7" style="stroke:none;"
      >SNR(dB)</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:8;"
    ><path d="M146 109.3516 L191.6842 109.3516 L237.3684 107.6772 L283.0526 168.8705 L328.7368 123.965 L374.4211 249.5484 L420.1053 580.9362 L465.7895 630.4692 L511.4737 460.3 L557.1579 496.8562 L602.8421 223.3661 L648.5263 194.4439 L694.2105 193.9111 L739.8947 186.3761 L785.5789 291.5617 L831.2632 173.4372 L876.9474 230.7489 L922.6316 498.2794 L968.3158 212.9389 L1014 210.7317" style="fill:none; fill-rule:evenodd;"
      /><circle transform="translate(146,109.3516)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(191.6842,109.3516)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(237.3684,107.6772)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(283.0526,168.8705)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(328.7368,123.965)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(374.4211,249.5484)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(420.1053,580.9362)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(465.7895,630.4692)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(511.4737,460.3)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(557.1579,496.8562)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(602.8421,223.3661)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(648.5263,194.4439)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(694.2105,193.9111)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(739.8947,186.3761)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(785.5789,291.5617)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(831.2632,173.4372)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(876.9474,230.7489)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(922.6316,498.2794)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(968.3158,212.9389)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
      /><circle transform="translate(1014,210.7317)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g style="fill:white; text-rendering:optimizeSpeed; color-rendering:optimizeSpeed; image-rendering:optimizeSpeed; shape-rendering:crispEdges; stroke:white; color-interpolation:sRGB;"
    ><path style="stroke:none;" d="M1001 728 L1001 687 L743 687 L743 728 Z"
    /></g
    ><g transform="translate(837,707.5)" style="font-size:28.8px; text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; font-family:'Times New Roman'; color-interpolation:linearRGB;"
    ><text x="0" xml:space="preserve" y="10.5" style="stroke:none;"
      >60G-16QAM</text
    ></g
    ><g style="stroke-linecap:butt; fill:rgb(0,114,189); text-rendering:geometricPrecision; image-rendering:optimizeQuality; color-rendering:optimizeQuality; stroke-linejoin:round; stroke:rgb(0,114,189); color-interpolation:linearRGB; stroke-width:8;"
    ><line y2="707.5" style="fill:none;" x1="751" x2="831" y1="707.5"
      /><circle transform="translate(791,707.5)" style="fill:none; stroke-linejoin:miter;" r="4" cx="0" cy="0"
    /></g
    ><g style="stroke-linecap:butt; fill:rgb(38,38,38); text-rendering:geometricPrecision; color-rendering:optimizeQuality; image-rendering:optimizeQuality; stroke:rgb(38,38,38); color-interpolation:linearRGB; stroke-width:1.3333;"
    ><path d="M743 728 L743 687 L1001 687 L1001 728 Z" style="fill:none; fill-rule:evenodd;"
    /></g
  ></g
></svg
>
